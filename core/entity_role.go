package core

import (
	"errors"
	"strings"
)

var ErrInvalidEntityRole = NewBusinessError("invalid entity role")

type EntityRole struct {
	value string
}

const (
	EntityRoleUser         = "user"
	EntityRolePro          = "pro"
	EntityRoleProFreelancer = "pro_freelancer"
	EntityRoleProTeamMember = "pro_team_member"
	EntityRoleTeamMember   = "team_member"
	EntityRoleBranch       = "branch"
	EntityRoleBrand        = "brand"
	EntityRoleBusiness     = "business"
)

var validEntityRoles = map[string]bool{
	EntityRoleUser:          true,
	EntityRolePro:           true,
	EntityRoleProFreelancer: true,
	EntityRoleProTeamMember: true,
	EntityRoleTeamMember:    true,
	EntityRoleBranch:        true,
	EntityRoleBrand:         true,
	EntityRoleBusiness:      true,
}

func NewEntityRole(v string) (*EntityRole, error) {
	normalized := strings.TrimSpace(strings.ToLower(v))
	if normalized == "" {
		return nil, errors.Join(ErrInvalidEntityRole, errors.New("entity role cannot be empty"))
	}

	if !validEntityRoles[normalized] {
		return nil, errors.Join(ErrInvalidEntityRole, errors.New("unknown entity role: "+v))
	}

	return &EntityRole{
		value: normalized,
	}, nil
}

func (e EntityRole) Value() string {
	return e.value
}

func (e EntityRole) IsValid() bool {
	return validEntityRoles[e.value]
}

func (e EntityRole) String() string {
	return e.value
}

// GetAllEntityRoles returns all valid entity roles
func GetAllEntityRoles() []string {
	roles := make([]string, 0, len(validEntityRoles))
	for role := range validEntityRoles {
		roles = append(roles, role)
	}
	return roles
}
