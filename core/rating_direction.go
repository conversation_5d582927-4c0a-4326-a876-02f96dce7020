package core

import (
	"errors"
	"strings"
)

var ErrInvalidRatingDirection = NewBusinessError("invalid rating direction")

type RatingDirection struct {
	value string
}

const (
	RatingDirectionGiven    = "given"
	RatingDirectionReceived = "received"
)

var validRatingDirections = map[string]bool{
	RatingDirectionGiven:    true,
	RatingDirectionReceived: true,
}

func NewRatingDirection(v string) (*RatingDirection, error) {
	normalized := strings.TrimSpace(strings.ToLower(v))
	if normalized == "" {
		return nil, errors.Join(ErrInvalidRatingDirection, errors.New("rating direction cannot be empty"))
	}

	if !validRatingDirections[normalized] {
		return nil, errors.Join(ErrInvalidRatingDirection, errors.New("unknown rating direction: "+v))
	}

	return &RatingDirection{
		value: normalized,
	}, nil
}

func (r RatingDirection) Value() string {
	return r.value
}

func (r RatingDirection) IsValid() bool {
	return validRatingDirections[r.value]
}

func (r RatingDirection) String() string {
	return r.value
}

// GetAllRatingDirections returns all valid rating directions
func GetAllRatingDirections() []string {
	directions := make([]string, 0, len(validRatingDirections))
	for direction := range validRatingDirections {
		directions = append(directions, direction)
	}
	return directions
}
