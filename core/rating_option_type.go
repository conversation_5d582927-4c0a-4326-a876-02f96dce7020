package core

import (
	"errors"
	"strings"
)

var ErrInvalidOptionType = NewBusinessError("invalid option type")

type OptionType struct {
	value string
}

const (
	OptionTypeOverallExperience      = "overall_experience"
	OptionTypeProfessionalSkills     = "professional_skills"
	OptionTypePersonalSkills         = "personal_skills"
	OptionTypeBranchOverallQuality   = "branch_overall_quality"
	OptionTypeBranchFacilities       = "branch_facilities"
	OptionTypeBranchStaffSupport     = "branch_staff_support"
	OptionTypeBranchRecommendation   = "branch_recommendation"
	OptionTypeBranchDesign           = "branch_design"
	OptionTypeBranchTeamProfessionalism = "branch_team_professionalism"
	OptionTypeBranchLocation         = "branch_location"
	OptionTypeBranchComfort          = "branch_comfort"
	OptionTypeTeamOverallTeamwork    = "team_overall_teamwork"
	OptionTypeTeamOverallTeamCulture = "team_overall_team_culture"
	OptionTypeTeamOverallCoordination = "team_overall_coordination"
	OptionTypeTeamOverallCommunication = "team_overall_communication"
	OptionTypeTeamOverallProfessionalism = "team_overall_professionalism"
	OptionTypeTeamOverallAtmosphere  = "team_overall_atmosphere"
	OptionTypeTeamOverallRespect     = "team_overall_respect"
	OptionTypeTeamOverallEfficiency  = "team_overall_efficiency"
)

var validOptionTypes = map[string]bool{
	OptionTypeOverallExperience:         true,
	OptionTypeProfessionalSkills:        true,
	OptionTypePersonalSkills:            true,
	OptionTypeBranchOverallQuality:      true,
	OptionTypeBranchFacilities:          true,
	OptionTypeBranchStaffSupport:        true,
	OptionTypeBranchRecommendation:      true,
	OptionTypeBranchDesign:              true,
	OptionTypeBranchTeamProfessionalism: true,
	OptionTypeBranchLocation:            true,
	OptionTypeBranchComfort:             true,
	OptionTypeTeamOverallTeamwork:       true,
	OptionTypeTeamOverallTeamCulture:    true,
	OptionTypeTeamOverallCoordination:   true,
	OptionTypeTeamOverallCommunication:  true,
	OptionTypeTeamOverallProfessionalism: true,
	OptionTypeTeamOverallAtmosphere:     true,
	OptionTypeTeamOverallRespect:        true,
	OptionTypeTeamOverallEfficiency:     true,
}

func NewOptionType(v string) (*OptionType, error) {
	normalized := strings.TrimSpace(strings.ToLower(v))
	if normalized == "" {
		return nil, errors.Join(ErrInvalidOptionType, errors.New("option type cannot be empty"))
	}

	if !validOptionTypes[normalized] {
		return nil, errors.Join(ErrInvalidOptionType, errors.New("unknown option type: "+v))
	}

	return &OptionType{
		value: normalized,
	}, nil
}

func (o OptionType) Value() string {
	return o.value
}

func (o OptionType) IsValid() bool {
	return validOptionTypes[o.value]
}

func (o OptionType) String() string {
	return o.value
}

// GetAllOptionTypes returns all valid option types
func GetAllOptionTypes() []string {
	types := make([]string, 0, len(validOptionTypes))
	for optionType := range validOptionTypes {
		types = append(types, optionType)
	}
	return types
}
