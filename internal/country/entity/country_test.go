package entity_test

import (
	"gitlab.viswalslab.com/backend/rating/core"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/country/entity"
)

var validID = core.NewID()

func TestNewCountry(t *testing.T) {
	tests := []struct {
		name    string
		input   *entity.NewCountryInput
		wantErr bool
	}{
		{
			name: "Valid input",
			input: &entity.NewCountryInput{
				ID:              validID.Value(),
				Name:            "CountryName",
				ISOCode:         "ISO",
				CountryCode:     "+1",
				Icon:            "icon.png",
				Continent:       1,
				Latitude:        12.34,
				Longitude:       56.78,
				Enabled:         true,
				PhoneNumberMask: core.ToPtr(""),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			},
			wantErr: false,
		},
		{
			name: "Invalid ID",
			input: &entity.NewCountryInput{
				ID:              "invalid-id-!",
				Name:            "CountryName",
				ISOCode:         "ISO",
				CountryCode:     "+1",
				Icon:            "icon.png",
				Continent:       1,
				Latitude:        12.34,
				Longitude:       56.78,
				Enabled:         true,
				PhoneNumberMask: core.ToPtr(""),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			},
			wantErr: true,
		},
		{
			name: "Invalid Latitude",
			input: &entity.NewCountryInput{
				ID:              validID.Value(),
				Name:            "CountryName",
				ISOCode:         "ISO",
				CountryCode:     "+1",
				Icon:            "icon.png",
				Continent:       1,
				Latitude:        999.99, // Invalid latitude
				Longitude:       56.78,
				Enabled:         true,
				PhoneNumberMask: core.ToPtr(""),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			},
			wantErr: true,
		},
		{
			name: "Invalid Longitude",
			input: &entity.NewCountryInput{
				ID:              validID.Value(),
				Name:            "CountryName",
				ISOCode:         "ISO",
				CountryCode:     "+1",
				Icon:            "icon.png",
				Continent:       1,
				Latitude:        12.34,
				Longitude:       999.99, // Invalid longitude
				Enabled:         true,
				PhoneNumberMask: core.ToPtr(""),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			country, err := entity.NewCountry(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assertCountry(t, tt.input, country)
			}
		})
	}
}

func assertCountry(t *testing.T, input *entity.NewCountryInput, country *entity.Country) {
	assert.Equal(t, input.ID, country.ID())
	assert.Equal(t, input.Name, country.Name())
	assert.Equal(t, input.ISOCode, country.ISOCode())
	assert.Equal(t, input.CountryCode, country.CountryCode())
	assert.Equal(t, input.Icon, country.Icon())
	assert.Equal(t, input.Continent, country.Continent())
	assert.Equal(t, input.Latitude, country.Latitude())
	assert.Equal(t, input.Longitude, country.Longitude())
	assert.Equal(t, input.Enabled, country.Enabled())
	assert.Equal(t, input.PhoneNumberMask, country.PhoneNumberMask())
	assert.Equal(t, input.CreatedAt.UTC(), country.CreatedAt().UTC())
	assert.Equal(t, input.UpdatedAt.UTC(), country.UpdatedAt().UTC())
}
