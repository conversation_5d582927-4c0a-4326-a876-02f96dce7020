package country

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/rating/internal/country/repository"
	"gitlab.viswalslab.com/backend/rating/internal/country/transport"
	"gitlab.viswalslab.com/backend/rating/internal/country/usecase"
)

type factory struct {
	db *sqlx.DB
}

func NewFactory(db *sqlx.DB) *factory {
	return &factory{
		db: db,
	}
}

func (f *factory) SearchCountriesHandler() *transport.SearchCountriesHandler {
	repo := repository.NewCountryRepo(f.db)
	uc := usecase.NewSearchCountriesUsecase(repo)
	h := transport.NewSearchCountriesHandler(uc)

	return h
}
