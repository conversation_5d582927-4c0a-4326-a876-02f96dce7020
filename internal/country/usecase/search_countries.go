package usecase

import (
	"context"
	"database/sql"
	"errors"
	"gitlab.viswalslab.com/backend/rating/core"
	"gitlab.viswalslab.com/backend/rating/internal/country/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"time"
)

//go:generate mockgen -source search_countries.go -destination ../../../mocks/internal_mock/country/usecase/search_countries.go -package usecase

var searchCountriesUsecaseName = "SearchCountriesUsecase"

type searchCountriesRepo interface {
	SearchCountries(ctx context.Context, filter *repository.SearchCountryFilter) (*repository.SearchCountriesResponse, error)
}

type SearchCountriesUsecase struct {
	repo searchCountriesRepo
}

func NewSearchCountriesUsecase(repo searchCountriesRepo) *SearchCountriesUsecase {
	return &SearchCountriesUsecase{
		repo: repo,
	}
}

func (uc *SearchCountriesUsecase) Execute(ctx context.Context, input *SearchCountriesInput) (*SearchCountriesOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", searchCountriesUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	var filter *repository.SearchCountryFilter
	if input == nil {
		filter = nil
	} else {
		filter = &repository.SearchCountryFilter{
			Enabled: input.Enabled,
		}
	}

	countries, err := uc.repo.SearchCountries(ctx, filter)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			logger.Debug("no countries found", vlog.F("input", input))
			return &SearchCountriesOutput{Data: []*SearchCountriesModel{}}, nil
		}
		err = core.ParseDBError(err)
		logger.Error("failed to get all countries", vlog.F("error", err))
		return nil, err
	}

	out := parseSearchCountriesResponse(countries)
	logger.Debug("execution finished")
	return out, nil
}

type SearchCountriesInput struct {
	Enabled *bool
}

type SearchCountriesModel struct {
	ID              string
	Name            string
	ISOCode         string
	CountryCode     string
	Icon            string
	Continent       int
	Latitude        float64
	Longitude       float64
	Enabled         bool
	PhoneNumberMask *string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}
type SearchCountriesOutput struct {
	Data []*SearchCountriesModel
}

func parseSearchCountriesResponse(input *repository.SearchCountriesResponse) *SearchCountriesOutput {
	var countries []*SearchCountriesModel
	for _, country := range input.Countries {
		countries = append(countries, &SearchCountriesModel{
			ID:              country.ID,
			Name:            country.Name,
			ISOCode:         country.ISOCode,
			CountryCode:     country.CountryCode,
			Icon:            country.Icon,
			Continent:       country.Continent,
			Latitude:        country.Latitude,
			Longitude:       country.Longitude,
			Enabled:         country.Enabled,
			PhoneNumberMask: country.PhoneNumberMask,
			CreatedAt:       country.CreatedAt,
			UpdatedAt:       country.UpdatedAt,
		})
	}
	return &SearchCountriesOutput{Data: countries}
}
