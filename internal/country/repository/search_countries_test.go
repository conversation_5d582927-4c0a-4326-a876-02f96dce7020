package repository_test

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/country/repository"
)

func TestGetAllCountries(t *testing.T) {
	db, mock, cleanup := setupMockDB(t)
	defer cleanup()

	repo := repository.NewCountryRepo(db)

	t.Run("success", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "name", "iso_code", "country_code", "icon", "continent", "latitude", "longitude", "enabled", "created_at", "updated_at", "phone_number_mask"}).
			AddRow("1", "Country1", "ISO1", "+1", "icon1.png", 1, 12.34, 56.78, true, time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), nil).
			AddRow("2", "Country2", "ISO2", "+2", "icon2.png", 2, 23.45, 67.89, true, time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), nil)

		mock.ExpectQuery(`SELECT id, name, iso_code, country_code, icon, continent, latitude, longitude, enabled, created_at, updated_at, phone_number_mask FROM country`).
			WillReturnRows(rows).RowsWillBeClosed()

		resp, err := repo.SearchCountries(context.Background(), nil)
		assert.NoError(t, err)
		assert.Len(t, resp.Countries, 2)
		assert.Equal(t, "Country1", resp.Countries[0].Name)
		assert.Equal(t, "Country2", resp.Countries[1].Name)
	})

	t.Run("no rows", func(t *testing.T) {
		mock.ExpectQuery(`SELECT id, name, iso_code, country_code, icon, continent, latitude, longitude, enabled, created_at, updated_at, phone_number_mask FROM country`).WillReturnError(sql.ErrNoRows)

		resp, err := repo.SearchCountries(context.Background(), nil)
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	t.Run("query error", func(t *testing.T) {
		mock.ExpectQuery(`SELECT id, name, iso_code, country_code, icon, continent, latitude, longitude, enabled, created_at, updated_at, phone_number_mask FROM country`).
			WillReturnError(assert.AnError)

		resp, err := repo.SearchCountries(context.Background(), nil)
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	t.Run("row error", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "name", "iso_code", "country_code", "icon", "continent", "latitude", "longitude", "enabled", "created_at", "updated_at", "phone_number_mask"}).
			AddRow("1", "Country1", "ISO1", "+1", "icon1.png", 1, 12.34, 56.78, true, time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), nil).
			AddRow("2", "country2", "ISO2", "+2", "icon2.png", 2, 23.45, 67.89, true, time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC), nil).RowError(1, assert.AnError)

		mock.ExpectQuery(`SELECT id, name, iso_code, country_code, icon, continent, latitude, longitude, enabled, created_at, updated_at, phone_number_mask FROM country`).
			WillReturnRows(rows)

		resp, err := repo.SearchCountries(context.Background(), nil)
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	assert.NoError(t, mock.ExpectationsWereMet())
}
