package transport

import (
	"context"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/rating/internal/rating/usecase"
)

type getRatingOptionsUsecase interface {
	Execute(ctx context.Context, input *usecase.GetRatingOptionsInput) (*usecase.GetRatingOptionsOutput, error)
}

type GetRatingOptionsHandler struct {
	usecase getRatingOptionsUsecase
}

func NewGetRatingOptionsHandler(usecase getRatingOptionsUsecase) *GetRatingOptionsHandler {
	return &GetRatingOptionsHandler{
		usecase: usecase,
	}
}

func (h *GetRatingOptionsHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input GetRatingOptionsInput
		if err := c.Bind(&input); err != nil {
			return err
		}

		// Validate mandatory fields
		if len(input.ApplicableToRoles) == 0 {
			return c.JSON(400, map[string]string{"error": "applicable_to_roles is required"})
		}
		if input.MinStars == nil {
			return c.JSON(400, map[string]string{"error": "min_stars is required"})
		}

		usecaseInput := &usecase.GetRatingOptionsInput{
			OptionType:        input.OptionType,
			ApplicableToRoles: input.ApplicableToRoles,
			MinStars:          *input.MinStars,
			FieldTag:          input.FieldTag,
		}

		result, err := h.usecase.Execute(ctx, usecaseInput)
		if err != nil {
			return err
		}

		return c.JSON(200, result)
	}
}

type GetRatingOptionsInput struct {
	OptionType        *string  `json:"option_type"`
	ApplicableToRoles []string `json:"applicable_to_roles" validate:"required,min=1"`
	MinStars          *int     `json:"min_stars" validate:"required"`
	FieldTag          *string  `json:"field_tag"`
}
