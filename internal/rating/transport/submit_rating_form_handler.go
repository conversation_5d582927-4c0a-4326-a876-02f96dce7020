package transport

import (
	"context"
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/rating/internal/rating/usecase"
)

type submitRatingFormUsecase interface {
	Execute(ctx context.Context, input *usecase.SubmitRatingFormInput) (*usecase.SubmitRatingFormOutput, error)
}

type SubmitRatingFormHandler struct {
	usecase submitRatingFormUsecase
}

func NewSubmitRatingFormHandler(usecase submitRatingFormUsecase) *SubmitRatingFormHandler {
	return &SubmitRatingFormHandler{
		usecase: usecase,
	}
}

func (h *SubmitRatingFormHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input SubmitRatingFormInput
		if err := c.Bind(&input); err != nil {
			return err
		}

		// Validate required fields
		if input.FromRole == "" {
			return echo.NewHTTPError(400, "from_role is required")
		}
		if input.ToRole == "" {
			return echo.NewHTTPError(400, "to_role is required")
		}
		if input.StarRating < 1 || input.StarRating > 5 {
			return echo.NewHTTPError(400, "star_rating must be between 1 and 5")
		}
		if input.OverallExperience == "" {
			return echo.NewHTTPError(400, "overall_experience is required")
		}

		usecaseInput := &usecase.SubmitRatingFormInput{
			EventID:            input.EventID,
			FromRole:           input.FromRole,
			FromEntityID:       input.FromEntityID,
			ToRole:             input.ToRole,
			ToEntityID:         input.ToEntityID,
			FieldTag:           input.FieldTag,
			StarRating:         input.StarRating,
			OverallExperience:  input.OverallExperience,
			ProfessionalSkills: input.ProfessionalSkills,
			PersonalSkills:     input.PersonalSkills,
			ThankingNote:       input.ThankingNote,
			FormData:           input.FormData,
		}

		result, err := h.usecase.Execute(ctx, usecaseInput)
		if err != nil {
			return err
		}

		return c.JSON(201, result)
	}
}

type SubmitRatingFormInput struct {
	EventID            *string                `json:"event_id,omitempty"`
	FromRole           string                 `json:"from_role" validate:"required"`
	FromEntityID       *string                `json:"from_entity_id,omitempty"`
	ToRole             string                 `json:"to_role" validate:"required"`
	ToEntityID         *string                `json:"to_entity_id,omitempty"`
	FieldTag           *string                `json:"field_tag,omitempty"`
	StarRating         int                    `json:"star_rating" validate:"required,min=1,max=5"`
	OverallExperience  string                 `json:"overall_experience" validate:"required"`
	ProfessionalSkills *string                `json:"professional_skills,omitempty"`
	PersonalSkills     *string                `json:"personal_skills,omitempty"`
	ThankingNote       *string                `json:"thanking_note,omitempty"`
	FormData           map[string]interface{} `json:"form_data,omitempty"`
}
