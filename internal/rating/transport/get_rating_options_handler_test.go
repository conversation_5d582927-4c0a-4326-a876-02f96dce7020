package transport_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/rating/transport"
	"gitlab.viswalslab.com/backend/rating/internal/rating/usecase"
	"go.uber.org/mock/gomock"
)

// Mock usecase for testing
type mockGetRatingOptionsUsecase struct {
	ctrl     *gomock.Controller
	recorder *MockGetRatingOptionsUsecaseMockRecorder
}

type MockGetRatingOptionsUsecaseMockRecorder struct {
	mock *mockGetRatingOptionsUsecase
}

func NewMockGetRatingOptionsUsecase(ctrl *gomock.Controller) *mockGetRatingOptionsUsecase {
	mock := &mockGetRatingOptionsUsecase{ctrl: ctrl}
	mock.recorder = &MockGetRatingOptionsUsecaseMockRecorder{mock}
	return mock
}

func (m *mockGetRatingOptionsUsecase) EXPECT() *MockGetRatingOptionsUsecaseMockRecorder {
	return m.recorder
}

func (m *mockGetRatingOptionsUsecase) Execute(ctx context.Context, input *usecase.GetRatingOptionsInput) (*usecase.GetRatingOptionsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, input)
	ret0, _ := ret[0].(*usecase.GetRatingOptionsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockGetRatingOptionsUsecaseMockRecorder) Execute(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*mockGetRatingOptionsUsecase)(nil).Execute), ctx, input)
}

func TestGetRatingOptionsHandler_Handle(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
		setupMock      func(*mockGetRatingOptionsUsecase)
	}{
		{
			name: "success",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           3,
				"option_type":         "overall_experience",
			},
			expectedStatus: 200,
			setupMock: func(m *mockGetRatingOptionsUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.GetRatingOptionsOutput{}, nil)
			},
		},
		{
			name: "missing_applicable_to_roles",
			requestBody: map[string]interface{}{
				"min_stars":   3,
				"option_type": "overall_experience",
			},
			expectedStatus: 400,
			expectedError:  "applicable_to_roles is required",
			setupMock:      func(m *mockGetRatingOptionsUsecase) {},
		},
		{
			name: "missing_min_stars",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"option_type":         "overall_experience",
			},
			expectedStatus: 400,
			expectedError:  "min_stars is required",
			setupMock:      func(m *mockGetRatingOptionsUsecase) {},
		},
		{
			name: "min_stars_too_low",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           0,
				"option_type":         "overall_experience",
			},
			expectedStatus: 400,
			expectedError:  "min_stars must be between 1 and 5",
			setupMock:      func(m *mockGetRatingOptionsUsecase) {},
		},
		{
			name: "min_stars_too_high",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           6,
				"option_type":         "overall_experience",
			},
			expectedStatus: 400,
			expectedError:  "min_stars must be between 1 and 5",
			setupMock:      func(m *mockGetRatingOptionsUsecase) {},
		},
		{
			name: "min_stars_negative",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           -1,
				"option_type":         "overall_experience",
			},
			expectedStatus: 400,
			expectedError:  "min_stars must be between 1 and 5",
			setupMock:      func(m *mockGetRatingOptionsUsecase) {},
		},
		{
			name: "min_stars_valid_boundary_1",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           1,
				"option_type":         "overall_experience",
			},
			expectedStatus: 200,
			setupMock: func(m *mockGetRatingOptionsUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.GetRatingOptionsOutput{}, nil)
			},
		},
		{
			name: "min_stars_valid_boundary_5",
			requestBody: map[string]interface{}{
				"applicable_to_roles": []string{"pro", "team_member"},
				"min_stars":           5,
				"option_type":         "overall_experience",
			},
			expectedStatus: 200,
			setupMock: func(m *mockGetRatingOptionsUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.GetRatingOptionsOutput{}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUsecase := NewMockGetRatingOptionsUsecase(ctrl)
			tt.setupMock(mockUsecase)

			handler := transport.NewGetRatingOptionsHandler(mockUsecase)

			e := echo.New()

			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/rating-options", bytes.NewReader(requestBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Handle()(c)

			if tt.expectedStatus == 200 {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)
			} else {
				assert.NoError(t, err) // Handler returns JSON error, not Go error
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response map[string]string
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedError, response["error"])
			}
		})
	}
}
