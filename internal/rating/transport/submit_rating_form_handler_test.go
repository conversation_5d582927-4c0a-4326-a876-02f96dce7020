package transport_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/rating/transport"
	"gitlab.viswalslab.com/backend/rating/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"go.uber.org/mock/gomock"
)

// Mock usecase for testing
type mockSubmitRatingFormUsecase struct {
	ctrl     *gomock.Controller
	recorder *MockSubmitRatingFormUsecaseMockRecorder
}

type MockSubmitRatingFormUsecaseMockRecorder struct {
	mock *mockSubmitRatingFormUsecase
}

func NewMockSubmitRatingFormUsecase(ctrl *gomock.Controller) *mockSubmitRatingFormUsecase {
	mock := &mockSubmitRatingFormUsecase{ctrl: ctrl}
	mock.recorder = &MockSubmitRatingFormUsecaseMockRecorder{mock}
	return mock
}

func (m *mockSubmitRatingFormUsecase) EXPECT() *MockSubmitRatingFormUsecaseMockRecorder {
	return m.recorder
}

func (m *mockSubmitRatingFormUsecase) Execute(ctx context.Context, input *usecase.SubmitRatingFormInput) (*usecase.SubmitRatingFormOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, input)
	ret0, _ := ret[0].(*usecase.SubmitRatingFormOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockSubmitRatingFormUsecaseMockRecorder) Execute(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*mockSubmitRatingFormUsecase)(nil).Execute), ctx, input)
}

func TestSubmitRatingFormHandler_Handle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockUsecase := NewMockSubmitRatingFormUsecase(ctrl)
	handler := transport.NewSubmitRatingFormHandler(mockUsecase)

	t.Run("success", func(t *testing.T) {
		// Prepare request
		requestBody := map[string]interface{}{
			"from_role":          "user",
			"to_role":            "pro_freelancer",
			"star_rating":        5,
			"overall_experience": "Excellent",
			"form_data": map[string]interface{}{
				"appointment_id": "12345",
				"service_type":   "consultation",
			},
		}
		jsonBody, _ := json.Marshal(requestBody)

		req := httptest.NewRequest(http.MethodPost, "/ratings/submit", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		req = req.WithContext(ctx)

		rec := httptest.NewRecorder()
		e := echo.New()
		c := e.NewContext(req, rec)

		// Mock usecase response
		mockResponse := &usecase.SubmitRatingFormOutput{
			InteractionID: "test-interaction-id",
			FromRole:      "user",
			ToRole:        "pro_freelancer",
			StarRating:    5,
			CreatedAt:     time.Now(),
			FormData: map[string]interface{}{
				"appointment_id": "12345",
				"service_type":   "consultation",
			},
			Status:  "submitted",
			Message: "Rating form submitted successfully",
		}

		mockUsecase.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(mockResponse, nil)

		// Execute handler
		err := handler.Handle()(c)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, http.StatusCreated, rec.Code)

		var response usecase.SubmitRatingFormOutput
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "test-interaction-id", response.InteractionID)
		assert.Equal(t, "user", response.FromRole)
		assert.Equal(t, "pro_freelancer", response.ToRole)
		assert.Equal(t, 5, response.StarRating)
		assert.Equal(t, "submitted", response.Status)
		assert.Equal(t, "Rating form submitted successfully", response.Message)
	})

	t.Run("missing from_role", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"to_role":            "pro_freelancer",
			"star_rating":        5,
			"overall_experience": "Excellent",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req := httptest.NewRequest(http.MethodPost, "/ratings/submit", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		req = req.WithContext(ctx)

		rec := httptest.NewRecorder()
		e := echo.New()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)

		assert.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		assert.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Equal(t, "from_role is required", httpErr.Message)
	})

	t.Run("invalid star_rating", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"from_role":          "user",
			"to_role":            "pro_freelancer",
			"star_rating":        6, // Invalid rating > 5
			"overall_experience": "Excellent",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req := httptest.NewRequest(http.MethodPost, "/ratings/submit", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		req = req.WithContext(ctx)

		rec := httptest.NewRecorder()
		e := echo.New()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)

		assert.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		assert.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Equal(t, "star_rating must be between 1 and 5", httpErr.Message)
	})
}
