package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/rating/core"
	"gitlab.viswalslab.com/backend/rating/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

//go:generate mockgen -source get_rating_options.go -destination ../../../mocks/internal_mock/rating/usecase/get_rating_options.go -package usecase

var getRatingOptionsUsecaseName = "GetRatingOptionsUsecase"

type getRatingOptionsRepo interface {
	GetRatingOptions(ctx context.Context, filter *repository.GetRatingOptionsFilter) (*repository.GetRatingOptionsResponse, error)
}

type GetRatingOptionsUsecase struct {
	repo getRatingOptionsRepo
}

func NewGetRatingOptionsUsecase(repo getRatingOptionsRepo) *GetRatingOptionsUsecase {
	return &GetRatingOptionsUsecase{
		repo: repo,
	}
}

type GetRatingOptionsInput struct {
	OptionType        *string
	ApplicableToRoles []string
	Direction         *string
	MinStars          *int
	FieldTag          *string
}

type RatingOptionModel struct {
	ID                int      `json:"id"`
	OptionType        string   `json:"option_type"`
	Value             string   `json:"value"` // Keep JSON field as "value" for API compatibility
	ApplicableToRoles []string `json:"applicable_to_roles"`
	Direction         []string `json:"direction"`
	MinStars          *int     `json:"min_stars"`
	FieldTag          *string  `json:"field_tag"`
	DisplayOrder      int      `json:"display_order"`
}

type GetRatingOptionsOutput struct {
	Options []RatingOptionModel `json:"options"`
}

func (uc *GetRatingOptionsUsecase) Execute(ctx context.Context, input *GetRatingOptionsInput) (*GetRatingOptionsOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", getRatingOptionsUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	var filter *repository.GetRatingOptionsFilter
	if input != nil {
		filter = &repository.GetRatingOptionsFilter{
			OptionType:        input.OptionType,
			ApplicableToRoles: input.ApplicableToRoles,
			Direction:         input.Direction,
			MinStars:          input.MinStars,
			FieldTag:          input.FieldTag,
		}
	}

	response, err := uc.repo.GetRatingOptions(ctx, filter)
	if err != nil {
		err = core.ParseDBError(err)
		logger.Error("failed to get rating options", vlog.F("error", err))
		return nil, err
	}

	output := parseGetRatingOptionsResponse(response)
	logger.Debug("execution finished", vlog.F("options_count", len(output.Options)))
	return output, nil
}

func parseGetRatingOptionsResponse(input *repository.GetRatingOptionsResponse) *GetRatingOptionsOutput {
	var options []RatingOptionModel
	for _, option := range input.Options {
		options = append(options, RatingOptionModel{
			ID:                option.ID,
			OptionType:        option.OptionType,
			Value:             option.Value,
			ApplicableToRoles: []string(option.ApplicableToRoles), // Convert pq.StringArray to []string
			Direction:         []string(option.Direction),         // Convert pq.StringArray to []string
			MinStars:          option.MinStars,
			FieldTag:          option.FieldTag,
			DisplayOrder:      option.DisplayOrder,
		})
	}
	return &GetRatingOptionsOutput{Options: options}
}
