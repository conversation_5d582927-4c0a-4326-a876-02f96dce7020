package usecase

import (
	"context"
	"encoding/json"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/rating/core"
	"gitlab.viswalslab.com/backend/rating/internal/rating/repository"
	"gitlab.viswalslab.com/backend/rating/pkg/rabbitmq"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

//go:generate mockgen -source submit_rating_form.go -destination ../../../mocks/internal_mock/rating/usecase/submit_rating_form.go -package usecase

var submitRatingFormUsecaseName = "SubmitRatingFormUsecase"

type submitRatingFormRepo interface {
	SubmitRatingForm(ctx context.Context, input *repository.SubmitRatingFormInput) (*repository.SubmitRatingFormResponse, error)
}

type SubmitRatingFormUsecase struct {
	repo      submitRatingFormRepo
	publisher *rabbitmq.Publisher
}

func NewSubmitRatingFormUsecase(repo submitRatingFormRepo, publisher *rabbitmq.Publisher) *SubmitRatingFormUsecase {
	return &SubmitRatingFormUsecase{
		repo:      repo,
		publisher: publisher,
	}
}

type SubmitRatingFormInput struct {
	EventID            *string                `json:"event_id,omitempty"`
	FromRole           string                 `json:"from_role"`
	FromEntityID       *string                `json:"from_entity_id,omitempty"`
	ToRole             string                 `json:"to_role"`
	ToEntityID         *string                `json:"to_entity_id,omitempty"`
	FieldTag           *string                `json:"field_tag,omitempty"`
	StarRating         int                    `json:"star_rating"`
	OverallExperience  string                 `json:"overall_experience"`
	ProfessionalSkills *string                `json:"professional_skills,omitempty"`
	PersonalSkills     *string                `json:"personal_skills,omitempty"`
	ThankingNote       *string                `json:"thanking_note,omitempty"`
	FormData           map[string]interface{} `json:"form_data,omitempty"`
}

type SubmitRatingFormOutput struct {
	InteractionID string                 `json:"interaction_id"`
	FromRole      string                 `json:"from_role"`
	ToRole        string                 `json:"to_role"`
	StarRating    int                    `json:"star_rating"`
	CreatedAt     time.Time              `json:"created_at"`
	FormData      map[string]interface{} `json:"form_data,omitempty"`
	Status        string                 `json:"status"`
	Message       string                 `json:"message"`
}

type RatingFormQueueMessage struct {
	InteractionID      string                 `json:"interaction_id"`
	EventID            string                 `json:"event_id"`
	FromRole           string                 `json:"from_role"`
	FromEntityID       string                 `json:"from_entity_id"`
	ToRole             string                 `json:"to_role"`
	ToEntityID         string                 `json:"to_entity_id"`
	FieldTag           string                 `json:"field_tag"`
	StarRating         int                    `json:"star_rating"`
	OverallExperience  string                 `json:"overall_experience"`
	ProfessionalSkills *string                `json:"professional_skills,omitempty"`
	PersonalSkills     *string                `json:"personal_skills,omitempty"`
	ThankingNote       *string                `json:"thanking_note,omitempty"`
	FormData           map[string]interface{} `json:"form_data,omitempty"`
	SubmittedAt        time.Time              `json:"submitted_at"`
	CorrelationID      string                 `json:"correlation_id"`
}

func (uc *SubmitRatingFormUsecase) Execute(ctx context.Context, input *SubmitRatingFormInput) (*SubmitRatingFormOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", submitRatingFormUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate required fields
	if input.FromRole == "" {
		return nil, core.NewBusinessError("from_role is required")
	}
	if input.ToRole == "" {
		return nil, core.NewBusinessError("to_role is required")
	}
	if input.StarRating < 1 || input.StarRating > 5 {
		return nil, core.NewBusinessError("star_rating must be between 1 and 5")
	}
	if input.OverallExperience == "" {
		return nil, core.NewBusinessError("overall_experience is required")
	}

	// Set default values for optional fields
	eventID := "00000000-0000-0000-0000-000000000000"
	if input.EventID != nil {
		eventID = *input.EventID
	}

	fromEntityID := "00000000-0000-0000-0000-000000000000"
	if input.FromEntityID != nil {
		fromEntityID = *input.FromEntityID
	}

	toEntityID := "00000000-0000-0000-0000-000000000000"
	if input.ToEntityID != nil {
		toEntityID = *input.ToEntityID
	}

	fieldTag := "default"
	if input.FieldTag != nil {
		fieldTag = *input.FieldTag
	}

	// Prepare repository input
	repoInput := &repository.SubmitRatingFormInput{
		EventID:            eventID,
		FromRole:           input.FromRole,
		FromEntityID:       fromEntityID,
		ToRole:             input.ToRole,
		ToEntityID:         toEntityID,
		FieldTag:           fieldTag,
		StarRating:         input.StarRating,
		OverallExperience:  input.OverallExperience,
		ProfessionalSkills: input.ProfessionalSkills,
		PersonalSkills:     input.PersonalSkills,
		ThankingNote:       input.ThankingNote,
		FormData:           input.FormData,
	}

	// Save to database
	response, err := uc.repo.SubmitRatingForm(ctx, repoInput)
	if err != nil {
		err = core.ParseDBError(err)
		logger.Error("failed to submit rating form", vlog.F("error", err))
		return nil, err
	}

	// Publish to RabbitMQ queue
	correlationID := core.NewID().Value()
	queueMessage := RatingFormQueueMessage{
		InteractionID:      response.InteractionID,
		EventID:            eventID,
		FromRole:           input.FromRole,
		FromEntityID:       fromEntityID,
		ToRole:             input.ToRole,
		ToEntityID:         toEntityID,
		FieldTag:           fieldTag,
		StarRating:         input.StarRating,
		OverallExperience:  input.OverallExperience,
		ProfessionalSkills: input.ProfessionalSkills,
		PersonalSkills:     input.PersonalSkills,
		ThankingNote:       input.ThankingNote,
		FormData:           input.FormData,
		SubmittedAt:        response.CreatedAt,
		CorrelationID:      correlationID,
	}

	messageBody, err := json.Marshal(queueMessage)
	if err != nil {
		logger.Error("failed to marshal queue message", vlog.F("error", err))
		// Don't fail the request if queue publishing fails
	} else {
		publishing := amqp.Publishing{
			ContentType:   "application/json",
			Body:          messageBody,
			CorrelationId: correlationID,
			Timestamp:     time.Now(),
		}

		// Publish to rating form queue
		err = uc.publisher.Publish(ctx, "rating_forms", "rating.form.submitted", publishing)
		if err != nil {
			logger.Error("failed to publish to queue", vlog.F("error", err))
			// Don't fail the request if queue publishing fails
		} else {
			logger.Debug("successfully published to queue", vlog.F("correlation_id", correlationID))
		}
	}

	output := &SubmitRatingFormOutput{
		InteractionID: response.InteractionID,
		FromRole:      input.FromRole,
		ToRole:        input.ToRole,
		StarRating:    input.StarRating,
		CreatedAt:     response.CreatedAt,
		FormData:      input.FormData,
		Status:        "submitted",
		Message:       "Rating form submitted successfully",
	}

	logger.Debug("execution finished", vlog.F("output", output))
	return output, nil
}
