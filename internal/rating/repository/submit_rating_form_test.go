package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestSubmitRatingForm(t *testing.T) {
	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("success_all_three_tables", func(t *testing.T) {
		db, mock, cleanup := setupMockDB(t)
		defer cleanup()

		repo := NewRatingRepo(db)

		input := &SubmitRatingFormInput{
			EventID:            "550e8400-e29b-41d4-a716-446655440001",
			FromRole:           "user",
			FromEntityID:       "550e8400-e29b-41d4-a716-446655440002",
			ToRole:             "pro",
			ToEntityID:         "550e8400-e29b-41d4-a716-446655440003",
			FieldTag:           "service",
			StarRating:         5,
			OverallExperience:  "Excellent service",
			ProfessionalSkills: stringPtr("Outstanding technical skills"),
			PersonalSkills:     stringPtr("Great communication"),
			ThankingNote:       stringPtr("Thank you for the amazing work!"),
			FormData:           map[string]interface{}{"additional": "data"},
		}

		interactionID := "550e8400-e29b-41d4-a716-446655440004"
		logBookID := "550e8400-e29b-41d4-a716-446655440005"
		createdAt := time.Now()

		// Mock transaction begin
		mock.ExpectBegin()

		// Mock interaction creation
		mock.ExpectQuery(`INSERT INTO Interactions`).
			WithArgs(
				input.EventID,
				input.FromRole,
				input.FromEntityID,
				input.ToRole,
				input.ToEntityID,
				input.FieldTag,
				input.StarRating,
				input.OverallExperience,
				input.ProfessionalSkills,
				input.PersonalSkills,
				input.ThankingNote,
				sqlmock.AnyArg(), // form_data JSON
			).
			WillReturnRows(sqlmock.NewRows([]string{"interaction_id", "created_at"}).
				AddRow(interactionID, createdAt))

		// Mock log book entry creation
		mock.ExpectQuery(`INSERT INTO log_books`).
			WithArgs(
				input.ToEntityID,        // entity_id (receiving rating)
				input.ToRole,            // entity_role
				"rating_form_submitted", // event_type
				sqlmock.AnyArg(),        // event_data JSON
				"pending",               // processing_status
				sqlmock.AnyArg(),        // created_at
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(logBookID))

		// Mock log book interaction creation
		mock.ExpectExec(`INSERT INTO log_book_interactions`).
			WithArgs(logBookID, interactionID, "rating_submission", "given").
			WillReturnResult(sqlmock.NewResult(1, 1))

		// Mock transaction commit
		mock.ExpectCommit()

		response, err := repo.SubmitRatingForm(ctx, input)

		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, interactionID, response.InteractionID)
		assert.Equal(t, createdAt, response.CreatedAt)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("interaction_creation_failure", func(t *testing.T) {
		db, mock, cleanup := setupMockDB(t)
		defer cleanup()

		repo := NewRatingRepo(db)

		input := &SubmitRatingFormInput{
			EventID:           "550e8400-e29b-41d4-a716-446655440001",
			FromRole:          "user",
			FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
			ToRole:            "pro",
			ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
			FieldTag:          "service",
			StarRating:        5,
			OverallExperience: "Excellent service",
			FormData:          map[string]interface{}{"test": "data"},
		}

		// Mock transaction begin
		mock.ExpectBegin()

		// Mock interaction creation failure
		mock.ExpectQuery(`INSERT INTO Interactions`).
			WillReturnError(assert.AnError)

		// Mock transaction rollback
		mock.ExpectRollback()

		response, err := repo.SubmitRatingForm(ctx, input)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("log_book_creation_failure", func(t *testing.T) {
		db, mock, cleanup := setupMockDB(t)
		defer cleanup()

		repo := NewRatingRepo(db)

		input := &SubmitRatingFormInput{
			EventID:           "550e8400-e29b-41d4-a716-446655440001",
			FromRole:          "user",
			FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
			ToRole:            "pro",
			ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
			FieldTag:          "service",
			StarRating:        5,
			OverallExperience: "Excellent service",
			FormData:          map[string]interface{}{"test": "data"},
		}

		interactionID := "550e8400-e29b-41d4-a716-446655440004"
		createdAt := time.Now()

		// Mock transaction begin
		mock.ExpectBegin()

		// Mock successful interaction creation
		mock.ExpectQuery(`INSERT INTO Interactions`).
			WillReturnRows(sqlmock.NewRows([]string{"interaction_id", "created_at"}).
				AddRow(interactionID, createdAt))

		// Mock log book creation failure
		mock.ExpectQuery(`INSERT INTO log_books`).
			WillReturnError(assert.AnError)

		// Mock transaction rollback
		mock.ExpectRollback()

		response, err := repo.SubmitRatingForm(ctx, input)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("log_book_interaction_creation_failure", func(t *testing.T) {
		db, mock, cleanup := setupMockDB(t)
		defer cleanup()

		repo := NewRatingRepo(db)

		input := &SubmitRatingFormInput{
			EventID:           "550e8400-e29b-41d4-a716-446655440001",
			FromRole:          "user",
			FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
			ToRole:            "pro",
			ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
			FieldTag:          "service",
			StarRating:        5,
			OverallExperience: "Excellent service",
			FormData:          map[string]interface{}{"test": "data"},
		}

		interactionID := "550e8400-e29b-41d4-a716-446655440004"
		logBookID := "550e8400-e29b-41d4-a716-446655440005"
		createdAt := time.Now()

		// Mock transaction begin
		mock.ExpectBegin()

		// Mock successful interaction creation
		mock.ExpectQuery(`INSERT INTO Interactions`).
			WillReturnRows(sqlmock.NewRows([]string{"interaction_id", "created_at"}).
				AddRow(interactionID, createdAt))

		// Mock successful log book creation
		mock.ExpectQuery(`INSERT INTO log_books`).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(logBookID))

		// Mock log book interaction creation failure
		mock.ExpectExec(`INSERT INTO log_book_interactions`).
			WillReturnError(assert.AnError)

		// Mock transaction rollback
		mock.ExpectRollback()

		response, err := repo.SubmitRatingForm(ctx, input)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("invalid_form_data_json", func(t *testing.T) {
		db, _, cleanup := setupMockDB(t)
		defer cleanup()

		repo := NewRatingRepo(db)

		input := &SubmitRatingFormInput{
			EventID:           "550e8400-e29b-41d4-a716-446655440001",
			FromRole:          "user",
			FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
			ToRole:            "pro",
			ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
			FieldTag:          "service",
			StarRating:        5,
			OverallExperience: "Excellent service",
			FormData:          map[string]interface{}{"invalid": make(chan int)}, // Invalid JSON
		}

		response, err := repo.SubmitRatingForm(ctx, input)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Contains(t, err.Error(), "json: unsupported type")
	})
}

// Helper function
func stringPtr(s string) *string {
	return &s
}
