package repository_test

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/rating/repository"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock, func()) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")

	cleanup := func() {
		db.Close()
	}

	return sqlxDB, mock, cleanup
}

func TestNewRatingRepo(t *testing.T) {
	db, _, cleanup := setupMockDB(t)
	defer cleanup()

	t.Run("success", func(t *testing.T) {
		repo := repository.NewRatingRepo(db)
		assert.NotNil(t, repo)
	})

	t.Run("failure", func(t *testing.T) {
		repo := repository.NewRatingRepo(nil)
		assert.NotNil(t, repo)
	})
}
