package repository

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type SubmitRatingFormInput struct {
	EventID            string
	FromRole           string
	FromEntityID       string
	ToRole             string
	ToEntityID         string
	FieldTag           string
	StarRating         int
	OverallExperience  string
	ProfessionalSkills *string
	PersonalSkills     *string
	ThankingNote       *string
	FormData           map[string]interface{} // Additional form data
}

type SubmitRatingFormResponse struct {
	InteractionID string
	CreatedAt     time.Time
}

func (r *ratingRepo) SubmitRatingForm(ctx context.Context, input *SubmitRatingFormInput) (*SubmitRatingFormResponse, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "submitRatingForm"), vlog.F("action", "submit rating form"))

	// Convert form data to JSON
	formDataJSON, err := json.Marshal(input.FormData)
	if err != nil {
		logger.Error("failed to marshal form data", vlog.F("error", err))
		return nil, err
	}

	// Start transaction for atomic operations across all three tables
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction", vlog.F("error", err))
		return nil, err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 1. Insert into Interactions table
	interactionID, createdAt, err := r.createInteraction(ctx, tx, input, formDataJSON)
	if err != nil {
		logger.Error("failed to create interaction", vlog.F("error", err))
		return nil, err
	}

	// 2. Create log book entry for audit trail
	logBookID, err := r.createLogBookEntry(ctx, tx, input, interactionID, formDataJSON)
	if err != nil {
		logger.Error("failed to create log book entry", vlog.F("error", err))
		return nil, err
	}

	// 3. Link interaction to log book
	err = r.createLogBookInteraction(ctx, tx, logBookID, interactionID)
	if err != nil {
		logger.Error("failed to create log book interaction", vlog.F("error", err))
		return nil, err
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("failed to commit transaction", vlog.F("error", err))
		return nil, err
	}

	logger.Debug("rating form submitted successfully",
		vlog.F("interaction_id", interactionID),
		vlog.F("log_book_id", logBookID))

	return &SubmitRatingFormResponse{
		InteractionID: interactionID,
		CreatedAt:     createdAt,
	}, nil
}

// createInteraction inserts a new interaction record
func (r *ratingRepo) createInteraction(ctx context.Context, tx *sqlx.Tx, input *SubmitRatingFormInput, formDataJSON []byte) (string, time.Time, error) {
	query := `
		INSERT INTO Interactions (
			event_id, from_role, from_entity_id, to_role, to_entity_id,
			field_tag, star_rating, overall_experience, professional_skills,
			personal_skills, thanking_note, form_data
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		RETURNING interaction_id, created_at`

	var interactionID string
	var createdAt time.Time
	err := tx.QueryRowContext(
		ctx,
		query,
		input.EventID,
		input.FromRole,
		input.FromEntityID,
		input.ToRole,
		input.ToEntityID,
		input.FieldTag,
		input.StarRating,
		input.OverallExperience,
		input.ProfessionalSkills,
		input.PersonalSkills,
		input.ThankingNote,
		formDataJSON,
	).Scan(&interactionID, &createdAt)

	return interactionID, createdAt, err
}

// createLogBookEntry creates an audit log entry for the rating submission
func (r *ratingRepo) createLogBookEntry(ctx context.Context, tx *sqlx.Tx, input *SubmitRatingFormInput, interactionID string, formDataJSON []byte) (string, error) {
	// Create event data with all rating information for audit trail
	eventData := map[string]interface{}{
		"interaction_id":      interactionID,
		"event_id":            input.EventID,
		"from_role":           input.FromRole,
		"from_entity_id":      input.FromEntityID,
		"to_role":             input.ToRole,
		"to_entity_id":        input.ToEntityID,
		"field_tag":           input.FieldTag,
		"star_rating":         input.StarRating,
		"overall_experience":  input.OverallExperience,
		"professional_skills": input.ProfessionalSkills,
		"personal_skills":     input.PersonalSkills,
		"thanking_note":       input.ThankingNote,
		"form_data":           input.FormData,
		"submitted_at":        time.Now(),
	}

	eventDataJSON, err := json.Marshal(eventData)
	if err != nil {
		return "", err
	}

	query := `
		INSERT INTO log_books (
			entity_id, entity_role, event_type, event_data,
			processing_status, created_at
		) VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id`

	var logBookID string
	err = tx.QueryRowContext(ctx, query,
		input.ToEntityID,        // The entity receiving the rating
		input.ToRole,            // The role receiving the rating
		"rating_form_submitted", // Event type
		eventDataJSON,           // Event data
		"pending",               // Initial status
		time.Now(),              // Created at
	).Scan(&logBookID)

	return logBookID, err
}

// createLogBookInteraction links the interaction to the log book entry
func (r *ratingRepo) createLogBookInteraction(ctx context.Context, tx *sqlx.Tx, logBookID, interactionID string) error {
	query := `
		INSERT INTO log_book_interactions (
			log_book_id, interaction_id, interaction_type, direction
		) VALUES ($1, $2, $3, $4)`

	_, err := tx.ExecContext(ctx, query,
		logBookID,
		interactionID,
		"rating_submission", // Type of interaction
		"given",             // Direction (rating given)
	)

	return err
}
