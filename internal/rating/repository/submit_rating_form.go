package repository

import (
	"context"
	"encoding/json"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"time"
)

type SubmitRatingFormInput struct {
	EventID            string
	FromRole           string
	FromEntityID       string
	ToRole             string
	ToEntityID         string
	FieldTag           string
	StarRating         int
	OverallExperience  string
	ProfessionalSkills *string
	PersonalSkills     *string
	ThankingNote       *string
	FormData           map[string]interface{} // Additional form data
}

type SubmitRatingFormResponse struct {
	InteractionID string
	CreatedAt     time.Time
}

func (r *ratingRepo) SubmitRatingForm(ctx context.Context, input *SubmitRatingFormInput) (*SubmitRatingFormResponse, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "submitRatingForm"), vlog.F("action", "submit rating form"))
	
	// Convert form data to JSON
	formDataJSON, err := json.Marshal(input.FormData)
	if err != nil {
		logger.Error("failed to marshal form data", vlog.F("error", err))
		return nil, err
	}

	query := `
		INSERT INTO Interactions (
			event_id, from_role, from_entity_id, to_role, to_entity_id, 
			field_tag, star_rating, overall_experience, professional_skills, 
			personal_skills, thanking_note, form_data
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		RETURNING interaction_id, created_at`

	logger.Debug("executing submit rating form query", vlog.F("input", input))

	var response SubmitRatingFormResponse
	err = r.db.QueryRowxContext(
		ctx, 
		query,
		input.EventID,
		input.FromRole,
		input.FromEntityID,
		input.ToRole,
		input.ToEntityID,
		input.FieldTag,
		input.StarRating,
		input.OverallExperience,
		input.ProfessionalSkills,
		input.PersonalSkills,
		input.ThankingNote,
		formDataJSON,
	).Scan(&response.InteractionID, &response.CreatedAt)

	if err != nil {
		logger.Error("failed to submit rating form", vlog.F("error", err))
		return nil, err
	}

	logger.Debug("rating form submitted successfully", vlog.F("interaction_id", response.InteractionID))
	return &response, nil
}
