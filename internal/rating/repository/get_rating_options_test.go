package repository

import (
	"testing"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConvertDTOsToEntities(t *testing.T) {
	tests := []struct {
		name        string
		dtos        []RatingOptionDTO
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid DTOs conversion",
			dtos: []RatingOptionDTO{
				{
					ID:                1,
					OptionType:        "overall_experience",
					Value:             "Excellent service",
					ApplicableToRoles: pq.StringArray{"user", "pro"},
					Direction:         pq.StringArray{"given", "received"},
					MinStars:          intPtr(4),
					FieldTag:          stringPtr("service"),
					DisplayOrder:      1,
				},
				{
					ID:                2,
					OptionType:        "professional_skills",
					Value:             "Great technical skills",
					ApplicableToRoles: pq.StringArray{"pro_freelancer"},
					Direction:         pq.StringArray{"given"},
					MinStars:          nil,
					FieldTag:          nil,
					DisplayOrder:      2,
				},
			},
			expectError: false,
		},
		{
			name: "invalid option type in DTO",
			dtos: []RatingOptionDTO{
				{
					ID:                1,
					OptionType:        "invalid_type",
					Value:             "Test",
					ApplicableToRoles: pq.StringArray{"user"},
					Direction:         pq.StringArray{"given"},
					DisplayOrder:      1,
				},
			},
			expectError: true,
			errorMsg:    "unknown option type",
		},
		{
			name: "invalid role in DTO",
			dtos: []RatingOptionDTO{
				{
					ID:                1,
					OptionType:        "overall_experience",
					Value:             "Test",
					ApplicableToRoles: pq.StringArray{"invalid_role"},
					Direction:         pq.StringArray{"given"},
					DisplayOrder:      1,
				},
			},
			expectError: true,
			errorMsg:    "unknown entity role",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entities, err := convertDTOsToEntities(tt.dtos)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, entities)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, entities)
				assert.Len(t, entities, len(tt.dtos))

				// Verify the conversion worked correctly
				for i, entity := range entities {
					dto := tt.dtos[i]
					assert.Equal(t, dto.ID, entity.ID())
					assert.Equal(t, dto.OptionType, entity.OptionType())
					assert.Equal(t, dto.Value, entity.Value())
					assert.Equal(t, []string(dto.ApplicableToRoles), entity.ApplicableToRoles())
					assert.Equal(t, []string(dto.Direction), entity.Directions())
					assert.Equal(t, dto.MinStars, entity.MinStars())
					assert.Equal(t, dto.FieldTag, entity.FieldTag())
					assert.Equal(t, dto.DisplayOrder, entity.DisplayOrder())
				}
			}
		})
	}
}

func TestRatingOptionEntity_BusinessLogicIntegration(t *testing.T) {
	// Create a DTO that represents data from the database
	dto := RatingOptionDTO{
		ID:                1,
		OptionType:        "overall_experience",
		Value:             "Excellent service",
		ApplicableToRoles: pq.StringArray{"user", "pro_freelancer"},
		Direction:         pq.StringArray{"given"},
		MinStars:          intPtr(4),
		FieldTag:          stringPtr("service"),
		DisplayOrder:      1,
	}

	// Convert to entity
	entities, err := convertDTOsToEntities([]RatingOptionDTO{dto})
	require.NoError(t, err)
	require.Len(t, entities, 1)

	entity := entities[0]

	t.Run("entity encapsulates business logic", func(t *testing.T) {
		// Test business logic methods that weren't available in the DTO
		assert.True(t, entity.IsApplicableToRole("user"))
		assert.True(t, entity.IsApplicableToRole("pro_freelancer"))
		assert.False(t, entity.IsApplicableToRole("branch"))

		assert.True(t, entity.IsApplicableToDirection("given"))
		assert.False(t, entity.IsApplicableToDirection("received"))

		assert.True(t, entity.IsApplicableToStarRating(4))
		assert.True(t, entity.IsApplicableToStarRating(5))
		assert.False(t, entity.IsApplicableToStarRating(3))

		assert.True(t, entity.IsApplicableToFieldTag(stringPtr("service")))
		assert.False(t, entity.IsApplicableToFieldTag(stringPtr("other")))
		assert.False(t, entity.IsApplicableToFieldTag(nil))
	})

	t.Run("entity provides comprehensive matching", func(t *testing.T) {
		// Test the comprehensive matching method
		assert.True(t, entity.Matches("user", "given", 4, stringPtr("service")))
		assert.True(t, entity.Matches("pro_freelancer", "given", 5, stringPtr("service")))

		// Should fail on any single criterion
		assert.False(t, entity.Matches("branch", "given", 4, stringPtr("service")))  // wrong role
		assert.False(t, entity.Matches("user", "received", 4, stringPtr("service"))) // wrong direction
		assert.False(t, entity.Matches("user", "given", 3, stringPtr("service")))    // insufficient stars
		assert.False(t, entity.Matches("user", "given", 4, stringPtr("other")))      // wrong field tag
	})

	t.Run("entity validates data integrity", func(t *testing.T) {
		// The entity should have validated the data during creation
		assert.Equal(t, 1, entity.ID())
		assert.Equal(t, "overall_experience", entity.OptionType())
		assert.Equal(t, "Excellent service", entity.Value())
		assert.Equal(t, []string{"user", "pro_freelancer"}, entity.ApplicableToRoles())
		assert.Equal(t, []string{"given"}, entity.Directions())
		assert.Equal(t, intPtr(4), entity.MinStars())
		assert.Equal(t, stringPtr("service"), entity.FieldTag())
		assert.Equal(t, 1, entity.DisplayOrder())
	})
}

func TestRatingOptionEntity_DomainValidation(t *testing.T) {
	t.Run("entity prevents invalid data", func(t *testing.T) {
		// Try to create an entity with invalid data that might come from a corrupted database
		invalidDTO := RatingOptionDTO{
			ID:                1,
			OptionType:        "invalid_option_type",
			Value:             "Test",
			ApplicableToRoles: pq.StringArray{"user"},
			Direction:         pq.StringArray{"given"},
			DisplayOrder:      1,
		}

		entities, err := convertDTOsToEntities([]RatingOptionDTO{invalidDTO})
		assert.Error(t, err)
		assert.Nil(t, entities)
		assert.Contains(t, err.Error(), "unknown option type")
	})

	t.Run("entity enforces business rules", func(t *testing.T) {
		// Try to create an entity with invalid star rating
		invalidDTO := RatingOptionDTO{
			ID:                1,
			OptionType:        "overall_experience",
			Value:             "Test",
			ApplicableToRoles: pq.StringArray{"user"},
			Direction:         pq.StringArray{"given"},
			MinStars:          intPtr(10), // Invalid: > 5
			DisplayOrder:      1,
		}

		entities, err := convertDTOsToEntities([]RatingOptionDTO{invalidDTO})
		assert.Error(t, err)
		assert.Nil(t, entities)
		assert.Contains(t, err.Error(), "rating must be between 0-5")
	})
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func stringPtr(s string) *string {
	return &s
}
