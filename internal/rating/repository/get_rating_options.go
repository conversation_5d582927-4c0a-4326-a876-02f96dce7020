package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/lib/pq"
	"gitlab.viswalslab.com/backend/rating/internal/rating/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// RatingOptionDTO represents the database structure for rating options
type RatingOptionDTO struct {
	ID                int            `db:"id"`
	OptionType        string         `db:"option_type"`
	Value             string         `db:"option_value"`
	ApplicableToRoles pq.StringArray `db:"applicable_to_roles"`
	Direction         pq.StringArray `db:"direction"`
	MinStars          *int           `db:"min_stars"`
	FieldTag          *string        `db:"field_tag"`
	DisplayOrder      int            `db:"display_order"`
	CreatedAt         time.Time      `db:"created_at"`
}

type GetRatingOptionsFilter struct {
	OptionType        *string
	ApplicableToRoles []string
	Direction         *string
	MinStars          *int
	FieldTag          *string
}

type GetRatingOptionsResponse struct {
	Options []*entity.RatingOption
}

func (r *ratingRepo) GetRatingOptions(ctx context.Context, filter *GetRatingOptionsFilter) (*GetRatingOptionsResponse, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "getRatingOptions"), vlog.F("action", "get rating options with filter"))

	query := `
		SELECT id, option_type, option_value, applicable_to_roles, direction, min_stars, field_tag, display_order, created_at
		FROM rating_options
		WHERE 1 = 1`

	var args []any
	argIndex := 1

	if filter != nil {
		if filter.OptionType != nil {
			query += ` AND option_type = $` + fmt.Sprintf("%d", argIndex)
			args = append(args, *filter.OptionType)
			argIndex++
		}

		if len(filter.ApplicableToRoles) > 0 {
			query += ` AND applicable_to_roles && $` + fmt.Sprintf("%d", argIndex)
			args = append(args, pq.Array(filter.ApplicableToRoles))
			argIndex++
		}

		if filter.Direction != nil {
			query += ` AND $` + fmt.Sprintf("%d", argIndex) + ` = ANY(direction)`
			args = append(args, *filter.Direction)
			argIndex++
		}

		if filter.MinStars != nil {
			query += ` AND min_stars = $` + fmt.Sprintf("%d", argIndex)
			args = append(args, *filter.MinStars)
			argIndex++
		}

		if filter.FieldTag != nil {
			query += ` AND (field_tag IS NULL OR field_tag = $` + fmt.Sprintf("%d", argIndex) + `)`
			args = append(args, *filter.FieldTag)
			argIndex++
		}
	}

	query += ` ORDER BY display_order ASC`

	logger.Debug("executing get rating options query", vlog.F("query", query), vlog.F("args", args))

	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("failed to get rating options", vlog.F("error", err))
		return nil, err
	}
	defer rows.Close()

	var dtos []RatingOptionDTO
	for rows.Next() {
		var dto RatingOptionDTO
		err := rows.StructScan(&dto)
		if err != nil {
			logger.Error("failed to scan rating option", vlog.F("error", err))
			return nil, err
		}
		dtos = append(dtos, dto)
	}

	if err = rows.Err(); err != nil {
		logger.Error("error iterating rating options", vlog.F("error", err))
		return nil, err
	}

	// Convert DTOs to entities
	entities, err := convertDTOsToEntities(dtos)
	if err != nil {
		logger.Error("failed to convert DTOs to entities", vlog.F("error", err))
		return nil, err
	}

	logger.Debug("rating options retrieved successfully", vlog.F("count", len(entities)))
	return &GetRatingOptionsResponse{Options: entities}, nil
}

// convertDTOsToEntities converts database DTOs to domain entities
func convertDTOsToEntities(dtos []RatingOptionDTO) ([]*entity.RatingOption, error) {
	entities := make([]*entity.RatingOption, 0, len(dtos))

	for _, dto := range dtos {
		entityInput := &entity.NewRatingOptionInput{
			ID:                dto.ID,
			OptionType:        dto.OptionType,
			Value:             dto.Value,
			ApplicableToRoles: []string(dto.ApplicableToRoles),
			Directions:        []string(dto.Direction),
			MinStars:          dto.MinStars,
			FieldTag:          dto.FieldTag,
			DisplayOrder:      dto.DisplayOrder,
			CreatedAt:         dto.CreatedAt,
		}

		ratingOption, err := entity.NewRatingOption(entityInput)
		if err != nil {
			return nil, fmt.Errorf("failed to create entity from DTO (ID: %d): %w", dto.ID, err)
		}

		entities = append(entities, ratingOption)
	}

	return entities, nil
}
