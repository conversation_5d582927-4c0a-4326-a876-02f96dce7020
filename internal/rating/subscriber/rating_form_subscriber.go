package subscriber

import (
	"context"
	"encoding/json"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/rating/pkg/rabbitmq"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type RatingFormSubscriber struct {
	processor RatingFormProcessor
}

type RatingFormProcessor interface {
	ProcessRatingForm(ctx context.Context, message *RatingFormMessage) error
}

type RatingFormMessage struct {
	InteractionID      string                 `json:"interaction_id"`
	EventID            string                 `json:"event_id"`
	FromRole           string                 `json:"from_role"`
	FromEntityID       string                 `json:"from_entity_id"`
	ToRole             string                 `json:"to_role"`
	ToEntityID         string                 `json:"to_entity_id"`
	FieldTag           string                 `json:"field_tag"`
	StarRating         int                    `json:"star_rating"`
	OverallExperience  string                 `json:"overall_experience"`
	ProfessionalSkills *string                `json:"professional_skills,omitempty"`
	PersonalSkills     *string                `json:"personal_skills,omitempty"`
	ThankingNote       *string                `json:"thanking_note,omitempty"`
	FormData           map[string]interface{} `json:"form_data,omitempty"`
	SubmittedAt        time.Time              `json:"submitted_at"`
	CorrelationID      string                 `json:"correlation_id"`
}

func NewRatingFormSubscriber(processor RatingFormProcessor) *RatingFormSubscriber {
	return &RatingFormSubscriber{
		processor: processor,
	}
}

func (s *RatingFormSubscriber) QueueName() string {
	return "rating_forms"
}

func (s *RatingFormSubscriber) HandleMessage(ctx context.Context, delivery amqp.Delivery) error {
	logger := vlog.FromContext(ctx).With(
		vlog.F("subscriber", "RatingFormSubscriber"),
		vlog.F("correlation_id", delivery.CorrelationId),
		vlog.F("queue", s.QueueName()),
	)

	logger.Debug("received rating form message", vlog.F("message_id", delivery.MessageId))

	// Parse the message
	var message RatingFormMessage
	err := json.Unmarshal(delivery.Body, &message)
	if err != nil {
		logger.Error("failed to unmarshal rating form message", vlog.F("error", err))
		return err
	}

	logger.Debug("parsed rating form message", vlog.F("interaction_id", message.InteractionID))

	// Process the message
	err = s.processor.ProcessRatingForm(ctx, &message)
	if err != nil {
		logger.Error("failed to process rating form", vlog.F("error", err), vlog.F("interaction_id", message.InteractionID))
		return err
	}

	logger.Info("successfully processed rating form", vlog.F("interaction_id", message.InteractionID))
	return nil
}

// Ensure RatingFormSubscriber implements rabbitmq.Subscriber
var _ rabbitmq.Subscriber = (*RatingFormSubscriber)(nil)
