package rating

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/rating/internal/rating/processor"
	"gitlab.viswalslab.com/backend/rating/internal/rating/repository"
	"gitlab.viswalslab.com/backend/rating/internal/rating/subscriber"
	"gitlab.viswalslab.com/backend/rating/internal/rating/transport"
	"gitlab.viswalslab.com/backend/rating/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/rating/pkg/rabbitmq"
)

type factory struct {
	db        *sqlx.DB
	publisher *rabbitmq.Publisher
}

func NewFactory(db *sqlx.DB, publisher *rabbitmq.Publisher) *factory {
	return &factory{
		db:        db,
		publisher: publisher,
	}
}

func (f *factory) GetRatingOptionsHandler() *transport.GetRatingOptionsHandler {
	repo := repository.NewRatingRepo(f.db)
	uc := usecase.NewGetRatingOptionsUsecase(repo)
	h := transport.NewGetRatingOptionsHandler(uc)

	return h
}

func (f *factory) SubmitRatingFormHandler() *transport.SubmitRatingFormHandler {
	repo := repository.NewRatingRepo(f.db)
	uc := usecase.NewSubmitRatingFormUsecase(repo, f.publisher)
	h := transport.NewSubmitRatingFormHandler(uc)

	return h
}

func (f *factory) RatingFormSubscriber() *subscriber.RatingFormSubscriber {
	processor := processor.NewRatingFormProcessor(f.db)
	subscriber := subscriber.NewRatingFormSubscriber(processor)

	return subscriber
}
