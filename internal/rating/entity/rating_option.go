package entity

import (
	"errors"
	"time"

	"gitlab.viswalslab.com/backend/rating/core"
)

var (
	ErrInvalidRatingOption = core.NewBusinessError("invalid rating option")
)

type RatingOption struct {
	id                int
	optionType        core.OptionType
	value             string
	applicableToRoles []core.EntityRole
	directions        []core.RatingDirection
	minStars          *core.Rating
	fieldTag          core.FieldTag
	displayOrder      int
	createdAt         core.Timestamp
}

type NewRatingOptionInput struct {
	ID                int
	OptionType        string
	Value             string
	ApplicableToRoles []string
	Directions        []string
	MinStars          *int
	FieldTag          *string
	DisplayOrder      int
	CreatedAt         time.Time
}

func NewRatingOption(input *NewRatingOptionInput) (*RatingOption, error) {
	if input == nil {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("input cannot be nil"))
	}

	// Validate and create option type
	optionType, err := core.NewOptionType(input.OptionType)
	if err != nil {
		return nil, errors.Join(ErrInvalidRatingOption, err)
	}

	// Validate value
	if input.Value == "" {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("value cannot be empty"))
	}

	// Validate and create applicable roles
	var applicableToRoles []core.EntityRole
	for _, roleStr := range input.ApplicableToRoles {
		role, err := core.NewEntityRole(roleStr)
		if err != nil {
			return nil, errors.Join(ErrInvalidRatingOption, err)
		}
		applicableToRoles = append(applicableToRoles, *role)
	}

	// Validate and create directions
	var directions []core.RatingDirection
	for _, dirStr := range input.Directions {
		direction, err := core.NewRatingDirection(dirStr)
		if err != nil {
			return nil, errors.Join(ErrInvalidRatingOption, err)
		}
		directions = append(directions, *direction)
	}

	// Validate and create min stars (optional)
	var minStars *core.Rating
	if input.MinStars != nil {
		rating, err := core.NewRating(float32(*input.MinStars))
		if err != nil {
			return nil, errors.Join(ErrInvalidRatingOption, err)
		}
		minStars = rating
	}

	// Validate and create field tag
	fieldTag, err := core.NewFieldTag(input.FieldTag)
	if err != nil {
		return nil, errors.Join(ErrInvalidRatingOption, err)
	}

	// Validate display order
	if input.DisplayOrder < 0 {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("display order cannot be negative"))
	}

	// Create timestamp
	createdAt, err := core.NewTimestampFromTime(input.CreatedAt)
	if err != nil {
		return nil, errors.Join(ErrInvalidRatingOption, err)
	}

	return &RatingOption{
		id:                input.ID,
		optionType:        *optionType,
		value:             input.Value,
		applicableToRoles: applicableToRoles,
		directions:        directions,
		minStars:          minStars,
		fieldTag:          *fieldTag,
		displayOrder:      input.DisplayOrder,
		createdAt:         *createdAt,
	}, nil
}

// Getter methods following the pattern from Country entity
func (r RatingOption) ID() int {
	return r.id
}

func (r RatingOption) OptionType() string {
	return r.optionType.Value()
}

func (r RatingOption) Value() string {
	return r.value
}

func (r RatingOption) ApplicableToRoles() []string {
	roles := make([]string, len(r.applicableToRoles))
	for i, role := range r.applicableToRoles {
		roles[i] = role.Value()
	}
	return roles
}

func (r RatingOption) Directions() []string {
	directions := make([]string, len(r.directions))
	for i, direction := range r.directions {
		directions[i] = direction.Value()
	}
	return directions
}

func (r RatingOption) MinStars() *int {
	if r.minStars == nil {
		return nil
	}
	stars := int(r.minStars.Value())
	return &stars
}

func (r RatingOption) FieldTag() *string {
	return r.fieldTag.Value()
}

func (r RatingOption) DisplayOrder() int {
	return r.displayOrder
}

func (r RatingOption) CreatedAt() time.Time {
	return r.createdAt.Value()
}

// Business logic methods

// IsApplicableToRole checks if this rating option applies to the given role
func (r RatingOption) IsApplicableToRole(role string) bool {
	entityRole, err := core.NewEntityRole(role)
	if err != nil {
		return false
	}

	for _, applicableRole := range r.applicableToRoles {
		if applicableRole.Value() == entityRole.Value() {
			return true
		}
	}
	return false
}

// IsApplicableToDirection checks if this rating option applies to the given direction
func (r RatingOption) IsApplicableToDirection(direction string) bool {
	if len(r.directions) == 0 {
		return true // No direction restriction means applies to all
	}

	ratingDirection, err := core.NewRatingDirection(direction)
	if err != nil {
		return false
	}

	for _, applicableDirection := range r.directions {
		if applicableDirection.Value() == ratingDirection.Value() {
			return true
		}
	}
	return false
}

// IsApplicableToStarRating checks if this rating option applies to the given star rating
func (r RatingOption) IsApplicableToStarRating(stars int) bool {
	if r.minStars == nil {
		return true // No minimum star restriction
	}
	return stars >= int(r.minStars.Value())
}

// IsApplicableToFieldTag checks if this rating option applies to the given field tag
func (r RatingOption) IsApplicableToFieldTag(fieldTag *string) bool {
	if r.fieldTag.IsNil() {
		return true // No field tag restriction means applies to all fields
	}

	if fieldTag == nil {
		return r.fieldTag.IsNil()
	}

	return r.fieldTag.String() == *fieldTag
}

// Matches checks if this rating option matches all the given criteria
func (r RatingOption) Matches(role, direction string, stars int, fieldTag *string) bool {
	return r.IsApplicableToRole(role) &&
		r.IsApplicableToDirection(direction) &&
		r.IsApplicableToStarRating(stars) &&
		r.IsApplicableToFieldTag(fieldTag)
}
