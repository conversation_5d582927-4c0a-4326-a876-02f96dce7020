package processor_test

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/rating/internal/rating/processor"
	"gitlab.viswalslab.com/backend/rating/internal/rating/subscriber"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestRatingFormProcessor_ProcessRatingForm(t *testing.T) {
	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	// Create mock database
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	processor := processor.NewRatingFormProcessor(sqlxDB)

	// Test message
	message := &subscriber.RatingFormMessage{
		InteractionID:     "550e8400-e29b-41d4-a716-446655440000",
		EventID:           "550e8400-e29b-41d4-a716-446655440001",
		FromRole:          "user",
		FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
		ToRole:            "pro_freelancer",
		ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
		FieldTag:          "skin_care",
		StarRating:        5,
		OverallExperience: "Excellent",
		SubmittedAt:       time.Now(),
		CorrelationID:     "550e8400-e29b-41d4-a716-446655440004",
	}

	// Mock transaction begin
	mock.ExpectBegin()

	// Mock log book entry creation
	logBookID := "550e8400-e29b-41d4-a716-446655440005"
	mock.ExpectQuery(`INSERT INTO log_books`).
		WithArgs(
			message.ToEntityID,
			message.ToRole,
			"rating_form_submitted",
			sqlmock.AnyArg(), // event_data JSON
			"processing",
			message.CorrelationID,
			sqlmock.AnyArg(), // created_at
		).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(logBookID))

	// Mock log book interaction creation
	mock.ExpectExec(`INSERT INTO log_book_interactions`).
		WithArgs(logBookID, message.InteractionID, "rating_submission", "given").
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock interaction status update
	mock.ExpectExec(`UPDATE Interactions`).
		WithArgs("processed", sqlmock.AnyArg(), message.InteractionID).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock entity rating stats update
	mock.ExpectExec(`INSERT INTO entity_rating_stats`).
		WithArgs(
			message.ToEntityID,
			message.ToRole,
			message.StarRating,
			message.SubmittedAt,
			message.StarRating,  // for ON CONFLICT update
			message.SubmittedAt, // for ON CONFLICT update
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock log book status update
	mock.ExpectExec(`UPDATE log_books`).
		WithArgs("processed", sqlmock.AnyArg(), logBookID).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock transaction commit
	mock.ExpectCommit()

	// Execute the processor
	err = processor.ProcessRatingForm(ctx, message)

	// Assertions
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRatingFormProcessor_ProcessRatingForm_LowRating(t *testing.T) {
	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	// Create mock database
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	processor := processor.NewRatingFormProcessor(sqlxDB)

	// Test message with low rating (no notification should be created)
	message := &subscriber.RatingFormMessage{
		InteractionID:     "550e8400-e29b-41d4-a716-446655440000",
		EventID:           "550e8400-e29b-41d4-a716-446655440001",
		FromRole:          "user",
		FromEntityID:      "550e8400-e29b-41d4-a716-446655440002",
		ToRole:            "pro_freelancer",
		ToEntityID:        "550e8400-e29b-41d4-a716-446655440003",
		FieldTag:          "skin_care",
		StarRating:        2, // Low rating
		OverallExperience: "Poor",
		SubmittedAt:       time.Now(),
		CorrelationID:     "550e8400-e29b-41d4-a716-446655440004",
	}

	// Mock transaction begin
	mock.ExpectBegin()

	// Mock log book entry creation
	logBookID := "550e8400-e29b-41d4-a716-446655440005"
	mock.ExpectQuery(`INSERT INTO log_books`).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(logBookID))

	// Mock log book interaction creation
	mock.ExpectExec(`INSERT INTO log_book_interactions`).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock interaction status update
	mock.ExpectExec(`UPDATE Interactions`).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock entity rating stats update
	mock.ExpectExec(`INSERT INTO entity_rating_stats`).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock log book status update
	mock.ExpectExec(`UPDATE log_books`).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Mock transaction commit
	mock.ExpectCommit()

	// Execute the processor
	err = processor.ProcessRatingForm(ctx, message)

	// Assertions
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
