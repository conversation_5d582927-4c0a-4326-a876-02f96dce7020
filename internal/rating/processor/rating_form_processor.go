package processor

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/rating/internal/rating/subscriber"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type RatingFormProcessor struct {
	db *sqlx.DB
}

func NewRatingFormProcessor(db *sqlx.DB) *RatingFormProcessor {
	return &RatingFormProcessor{
		db: db,
	}
}

func (p *RatingFormProcessor) ProcessRatingForm(ctx context.Context, message *subscriber.RatingFormMessage) error {
	logger := vlog.FromContext(ctx).With(
		vlog.F("processor", "RatingFormProcessor"),
		vlog.F("interaction_id", message.InteractionID),
		vlog.F("correlation_id", message.CorrelationID),
	)

	logger.Debug("starting to process rating form")

	// Start a database transaction for atomic operations
	tx, err := p.db.BeginTxx(ctx, nil)
	if err != nil {
		logger.Error("failed to start transaction", vlog.F("error", err))
		return err
	}
	defer tx.Rollback()

	// 1. Create log book entry for the rating submission event
	logBookID, err := p.createLogBookEntry(ctx, tx, message)
	if err != nil {
		logger.Error("failed to create log book entry", vlog.F("error", err))
		return err
	}

	// 2. Create log book interaction linking the interaction to the log entry
	err = p.createLogBookInteraction(ctx, tx, logBookID, message.InteractionID, "rating_submission", "given")
	if err != nil {
		logger.Error("failed to create log book interaction", vlog.F("error", err))
		return err
	}

	// 3. Update interaction status to "processed"
	err = p.updateInteractionStatus(ctx, tx, message.InteractionID, "processed")
	if err != nil {
		logger.Error("failed to update interaction status", vlog.F("error", err))
		return err
	}

	// 4. Update entity rating statistics
	err = p.updateEntityRatingStats(ctx, tx, message)
	if err != nil {
		logger.Error("failed to update entity rating stats", vlog.F("error", err))
		return err
	}

	// 6. Update log book entry status to "processed"
	err = p.updateLogBookStatus(ctx, tx, logBookID, "processed")
	if err != nil {
		logger.Error("failed to update log book status", vlog.F("error", err))
		return err
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("failed to commit transaction", vlog.F("error", err))
		return err
	}

	logger.Info("successfully processed rating form", vlog.F("log_book_id", logBookID))
	return nil
}

func (p *RatingFormProcessor) updateInteractionStatus(ctx context.Context, tx *sqlx.Tx, interactionID, status string) error {
	query := `
		UPDATE Interactions 
		SET processing_status = $1, processed_at = $2 
		WHERE interaction_id = $3`

	_, err := tx.ExecContext(ctx, query, status, time.Now(), interactionID)
	return err
}

func (p *RatingFormProcessor) createLogBookEntry(ctx context.Context, tx *sqlx.Tx, message *subscriber.RatingFormMessage) (string, error) {
	// Create event data with all rating information
	eventData := map[string]any{
		"interaction_id":      message.InteractionID,
		"event_id":            message.EventID,
		"from_role":           message.FromRole,
		"from_entity_id":      message.FromEntityID,
		"to_role":             message.ToRole,
		"to_entity_id":        message.ToEntityID,
		"field_tag":           message.FieldTag,
		"star_rating":         message.StarRating,
		"overall_experience":  message.OverallExperience,
		"professional_skills": message.ProfessionalSkills,
		"personal_skills":     message.PersonalSkills,
		"thanking_note":       message.ThankingNote,
		"form_data":           message.FormData,
		"submitted_at":        message.SubmittedAt,
	}

	eventDataJSON, _ := json.Marshal(eventData)

	query := `
		INSERT INTO log_books (
			entity_id, entity_role, event_type, event_data,
			processing_status, correlation_id, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id`

	var logBookID string
	err := tx.QueryRowContext(ctx, query,
		message.ToEntityID,      // The entity receiving the rating
		message.ToRole,          // The role receiving the rating
		"rating_form_submitted", // Event type
		eventDataJSON,           // Event data
		"processing",            // Initial status
		message.CorrelationID,   // Correlation ID from queue message
		time.Now(),              // Created at
	).Scan(&logBookID)

	return logBookID, err
}

func (p *RatingFormProcessor) createLogBookInteraction(ctx context.Context, tx *sqlx.Tx, logBookID, interactionID, interactionType, direction string) error {
	query := `
		INSERT INTO log_book_interactions (
			log_book_id, interaction_id, interaction_type, direction
		) VALUES ($1, $2, $3, $4)`

	_, err := tx.ExecContext(ctx, query, logBookID, interactionID, interactionType, direction)
	return err
}

func (p *RatingFormProcessor) updateLogBookStatus(ctx context.Context, tx *sqlx.Tx, logBookID, status string) error {
	query := `
		UPDATE log_books
		SET processing_status = $1, processed_at = $2
		WHERE id = $3`

	_, err := tx.ExecContext(ctx, query, status, time.Now(), logBookID)
	return err
}

func (p *RatingFormProcessor) updateEntityRatingStats(ctx context.Context, tx *sqlx.Tx, message *subscriber.RatingFormMessage) error {
	// Update or insert rating statistics for the entity
	query := `
		INSERT INTO entity_rating_stats (
			entity_id, entity_role, total_ratings, total_stars,
			average_rating, last_rating_at, updated_at
		) VALUES ($1, $2, 1, $3, $3, $4, $4)
		ON CONFLICT (entity_id, entity_role)
		DO UPDATE SET
			total_ratings = entity_rating_stats.total_ratings + 1,
			total_stars = entity_rating_stats.total_stars + $5,
			average_rating = (entity_rating_stats.total_stars + $5) / (entity_rating_stats.total_ratings + 1),
			last_rating_at = $6,
			updated_at = $6`

	_, err := tx.ExecContext(ctx, query,
		message.ToEntityID,  // $1
		message.ToRole,      // $2
		message.StarRating,  // $3 (for initial insert)
		message.SubmittedAt, // $4 (for initial insert)
		message.StarRating,  // $5 (for update)
		message.SubmittedAt, // $6 (for update)
	)
	return err
}

// Ensure RatingFormProcessor implements subscriber.RatingFormProcessor
var _ subscriber.RatingFormProcessor = (*RatingFormProcessor)(nil)
