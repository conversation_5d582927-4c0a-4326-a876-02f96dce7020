#!/bin/bash

# RabbitMQ initialization script for rating service
# This script creates the necessary exchanges, queues, and bindings

set -e

# RabbitMQ connection details
RABBITMQ_HOST=${RABBITMQ_HOST:-localhost}
RABBITMQ_PORT=${RABBITMQ_PORT:-15672}
RABBITMQ_USER=${RABBITMQ_USER:-user}
RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-password}
RABBITMQ_VHOST=${RABBITMQ_VHOST:-/}

# Rating service configuration
EXCHANGE_NAME="rating_forms"
QUEUE_NAME="rating_forms"
ROUTING_KEY="rating.form.submitted"

echo "🐰 Initializing RabbitMQ for Rating Service..."
echo "Host: $RABBITMQ_HOST:$RABBITMQ_PORT"
echo "User: $RABBITMQ_USER"
echo "VHost: $RABBITMQ_VHOST"

# Function to make RabbitMQ API calls
rabbitmq_api() {
    local method=$1
    local path=$2
    local data=$3
    
    curl -s -u "$RABBITMQ_USER:$RABBITMQ_PASSWORD" \
         -H "Content-Type: application/json" \
         -X "$method" \
         "http://$RABBITMQ_HOST:$RABBITMQ_PORT/api$path" \
         ${data:+-d "$data"}
}

# Wait for RabbitMQ to be ready
echo "⏳ Waiting for RabbitMQ to be ready..."
until rabbitmq_api GET "/overview" > /dev/null 2>&1; do
    echo "Waiting for RabbitMQ management API..."
    sleep 2
done
echo "✅ RabbitMQ is ready!"

# Create exchange
echo "📦 Creating exchange: $EXCHANGE_NAME"
rabbitmq_api PUT "/exchanges/$RABBITMQ_VHOST/$EXCHANGE_NAME" '{
    "type": "direct",
    "durable": true,
    "auto_delete": false,
    "arguments": {}
}'

# Create queue
echo "📋 Creating queue: $QUEUE_NAME"
rabbitmq_api PUT "/queues/$RABBITMQ_VHOST/$QUEUE_NAME" '{
    "durable": true,
    "auto_delete": false,
    "arguments": {}
}'

# Create binding
echo "🔗 Creating binding: $EXCHANGE_NAME -> $QUEUE_NAME with routing key: $ROUTING_KEY"
rabbitmq_api POST "/bindings/$RABBITMQ_VHOST/e/$EXCHANGE_NAME/q/$QUEUE_NAME" '{
    "routing_key": "'$ROUTING_KEY'",
    "arguments": {}
}'

# Verify setup
echo "🔍 Verifying setup..."
echo "Exchange info:"
rabbitmq_api GET "/exchanges/$RABBITMQ_VHOST/$EXCHANGE_NAME" | jq -r '.name // "Not found"'

echo "Queue info:"
rabbitmq_api GET "/queues/$RABBITMQ_VHOST/$QUEUE_NAME" | jq -r '.name // "Not found"'

echo "Bindings:"
rabbitmq_api GET "/queues/$RABBITMQ_VHOST/$QUEUE_NAME/bindings" | jq -r '.[].routing_key // "No bindings"'

echo "🎉 RabbitMQ setup completed successfully!"
echo ""
echo "📊 Summary:"
echo "  Exchange: $EXCHANGE_NAME (direct, durable)"
echo "  Queue: $QUEUE_NAME (durable)"
echo "  Binding: $ROUTING_KEY"
echo ""
echo "🚀 Your rating service is ready to publish and consume messages!"
