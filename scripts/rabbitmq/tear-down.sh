#!/bin/bash

# RabbitMQ tear-down script for rating service
# This script removes the exchanges, queues, and bindings

set -e

# RabbitMQ connection details
RABBITMQ_HOST=${RABBITMQ_HOST:-localhost}
RABBITMQ_PORT=${RABBITMQ_PORT:-15672}
RABBITMQ_USER=${RABBITMQ_USER:-user}
RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-password}
RABBITMQ_VHOST=${RABBITMQ_VHOST:-/}

# Rating service configuration
EXCHANGE_NAME="rating_forms"
QUEUE_NAME="rating_forms"

echo "🧹 Tearing down RabbitMQ setup for Rating Service..."
echo "Host: $RABBITMQ_HOST:$RABBITMQ_PORT"
echo "User: $RABBITMQ_USER"
echo "VHost: $RABBITMQ_VHOST"

# Function to make RabbitMQ API calls
rabbitmq_api() {
    local method=$1
    local path=$2
    
    curl -s -u "$RABBITMQ_USER:$RABBITMQ_PASSWORD" \
         -H "Content-Type: application/json" \
         -X "$method" \
         "http://$RABBITMQ_HOST:$RABBITMQ_PORT/api$path"
}

# Check if RabbitMQ is available
echo "⏳ Checking RabbitMQ availability..."
if ! rabbitmq_api GET "/overview" > /dev/null 2>&1; then
    echo "❌ RabbitMQ management API is not available"
    exit 1
fi
echo "✅ RabbitMQ is available!"

# Delete queue (this will also remove bindings)
echo "🗑️ Deleting queue: $QUEUE_NAME"
if rabbitmq_api GET "/queues/$RABBITMQ_VHOST/$QUEUE_NAME" > /dev/null 2>&1; then
    rabbitmq_api DELETE "/queues/$RABBITMQ_VHOST/$QUEUE_NAME"
    echo "✅ Queue deleted: $QUEUE_NAME"
else
    echo "ℹ️ Queue not found: $QUEUE_NAME"
fi

# Delete exchange
echo "🗑️ Deleting exchange: $EXCHANGE_NAME"
if rabbitmq_api GET "/exchanges/$RABBITMQ_VHOST/$EXCHANGE_NAME" > /dev/null 2>&1; then
    rabbitmq_api DELETE "/exchanges/$RABBITMQ_VHOST/$EXCHANGE_NAME"
    echo "✅ Exchange deleted: $EXCHANGE_NAME"
else
    echo "ℹ️ Exchange not found: $EXCHANGE_NAME"
fi

echo "🎉 RabbitMQ tear-down completed successfully!"
echo ""
echo "📊 Summary:"
echo "  Removed exchange: $EXCHANGE_NAME"
echo "  Removed queue: $QUEUE_NAME"
echo "  Removed all associated bindings"
