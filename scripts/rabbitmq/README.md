# RabbitMQ Setup for Rating Service

This directory contains scripts to set up and tear down RabbitMQ infrastructure for the rating microservice.

## 🚀 Quick Setup

### Option 1: Automatic Setup (Recommended)
```bash
# Start infrastructure and set up queues
make setup
```

### Option 2: Manual Setup
```bash
# Start Docker services
make infra

# Wait for RabbitMQ to be ready, then initialize queues
make initiate-queue
```

## 📋 What Gets Created

### Exchange
- **Name**: `rating_forms`
- **Type**: `direct`
- **Durable**: `true`
- **Auto-delete**: `false`

### Queue
- **Name**: `rating_forms`
- **Durable**: `true`
- **Auto-delete**: `false`

### Binding
- **Exchange**: `rating_forms`
- **Queue**: `rating_forms`
- **Routing Key**: `rating.form.submitted`

## 🔧 Manual Commands

### Initialize RabbitMQ
```bash
./scripts/rabbitmq/initial-script.sh
```

### Tear Down RabbitMQ
```bash
./scripts/rabbitmq/tear-down.sh
```

## 🌐 RabbitMQ Management UI

Access the management interface at: http://localhost:15672

**Default Credentials:**
- Username: `user`
- Password: `password`

## 🔍 Verification

After running the setup, you can verify the configuration:

1. **Via Management UI**: Visit http://localhost:15672
2. **Via API**: 
   ```bash
   curl -u user:password http://localhost:15672/api/queues/%2F/rating_forms
   ```
3. **Via Application Logs**: The rating service will log successful queue connections

## 🐛 Troubleshooting

### Queue Not Found Error
```
Exception (404) Reason: "NOT_FOUND - no queue 'rating_forms' in vhost '/'"
```

**Solution**: Run the initialization script:
```bash
make initiate-queue
```

### Connection Refused
```
Failed to connect to RabbitMQ
```

**Solution**: Ensure RabbitMQ is running:
```bash
docker compose up -d rabbitmq
```

### Permission Denied
```
Access refused for user 'user'
```

**Solution**: Check environment variables in `.env` file:
```bash
RABBITMQ_USER=user
RABBITMQ_PASSWORD=password
```

## 📝 Environment Variables

The scripts use these environment variables (with defaults):

```bash
RABBITMQ_HOST=localhost
RABBITMQ_PORT=15672
RABBITMQ_USER=user
RABBITMQ_PASSWORD=password
RABBITMQ_VHOST=/
```

## 🔄 Message Flow

1. **Rating Form Submission** → API saves to database
2. **Queue Publishing** → Message sent to `rating_forms` exchange
3. **Queue Routing** → Message routed to `rating_forms` queue via `rating.form.submitted` key
4. **Queue Processing** → Subscriber processes message and updates statistics

## 🧹 Cleanup

To remove all RabbitMQ resources:
```bash
make destroy-queue
make down
```
