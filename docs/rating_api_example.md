# Rating API

## Submit Rating Form Endpoint

**POST** `/rating/v1/ratings/submit`

### Description
This endpoint allows users to submit complete rating forms. The data is saved to the database and published to a RabbitMQ queue for further processing.

### Request Body

```json
{
  "from_role": "user",
  "to_role": "pro",
  "star_rating": 5,
  "overall_experience": "Excellent",
  "professional_skills": "Knowledgeable",
  "personal_skills": "Respectful",
  "thanking_note": "Great service!",
  "form_data": {
    "appointment_id": "12345",
    "service_type": "consultation",
    "duration": 60,
    "additional_notes": "Very satisfied with the service"
  }
}
```

### Required Fields

- `from_role` (string): Role of the entity giving the rating
- `to_role` (string): Role of the entity receiving the rating
- `star_rating` (integer): Rating value between 1-5
- `overall_experience` (string): Overall experience description (use values from `/ratings/options` endpoint)

### Optional Fields

- `event_id` (string): UUID of the event
- `from_entity_id` (string): UUID of the entity giving the rating
- `to_entity_id` (string): UUID of the entity receiving the rating
- `field_tag` (string): Context/field tag
- `professional_skills` (string): Professional skills feedback (for 5★ ratings only)
- `personal_skills` (string): Personal skills feedback (for 5★ ratings only)
- `thanking_note` (string): Thank you note
- `form_data` (object): Additional form data as key-value pairs

### Example Response

```json
{
  "interaction_id": "550e8400-e29b-41d4-a716-************",
  "from_role": "user",
  "to_role": "pro",
  "star_rating": 5,
  "created_at": "2024-06-30T10:30:00Z",
  "form_data": {
    "appointment_id": "12345",
    "service_type": "consultation",
    "duration": 60,
    "additional_notes": "Very satisfied with the service"
  },
  "status": "submitted",
  "message": "Rating form submitted successfully"
}
```

### RabbitMQ Integration

When a form is submitted, a message is published to the RabbitMQ queue with the following structure:

```json
{
  "interaction_id": "550e8400-e29b-41d4-a716-************",
  "event_id": "00000000-0000-0000-0000-000000000000",
  "from_role": "user",
  "from_entity_id": "00000000-0000-0000-0000-000000000000",
  "to_role": "pro",
  "to_entity_id": "00000000-0000-0000-0000-000000000000",
  "field_tag": "default",
  "star_rating": 5,
  "overall_experience": "Excellent",
  "professional_skills": "Knowledgeable",
  "personal_skills": "Respectful",
  "thanking_note": "Great service!",
  "form_data": {
    "appointment_id": "12345",
    "service_type": "consultation",
    "duration": 60,
    "additional_notes": "Very satisfied with the service"
  },
  "submitted_at": "2024-06-30T10:30:00Z",
  "correlation_id": "550e8400-e29b-41d4-a716-************"
}
```

**Queue Details:**
- Exchange: `rating_forms`
- Routing Key: `rating.form.submitted`
- Queue Name: `rating_forms`

### Queue Processing with Log Books Pattern

When the queue message is processed, the system follows this workflow:

1. **Creates Log Book Entry**: Records the event in `log_books` table with:
   - `event_type`: "rating_form_submitted"
   - `event_data`: Complete rating information as JSONB
   - `processing_status`: "processing" → "processed"
   - `correlation_id`: Links to the original queue message

2. **Creates Log Book Interaction**: Links the interaction to the log entry in `log_book_interactions` table

3. **Updates Interaction Status**: Marks the interaction as "processed"

4. **Creates Notifications**: For high ratings (4-5 stars)

5. **Updates Statistics**: Aggregates rating data in `entity_rating_stats`

This pattern provides:
- **Complete Audit Trail**: Every rating event is logged with full context
- **Event Sourcing**: Can reconstruct what happened and when
- **Traceability**: Links between interactions and log entries
- **Compliance**: Full data lineage for regulatory requirements

## Get Rating Options Endpoint

**POST** `/rating/v1/ratings/options`

### Description
This endpoint retrieves available rating options based on filters. It supports filtering by option type, applicable roles, minimum stars, and field tags.

### Request Body

```json
{
  "option_type": "overall_experience",
  "applicable_to_roles": ["pro", "team_member"],
  "min_stars": 5,
  "field_tag": "skin_care"
}
```

### Request Fields

- `option_type` (string, optional): Filter by option type ("overall_experience", "professional_skills", "personal_skills")
- `applicable_to_roles` (array, optional): Array of roles (["pro", "team_member"])
- `min_stars` (integer, optional): Filter by minimum star rating
- `field_tag` (string, optional): Filter by field tag

### Example Request

```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "option_type": "overall_experience",
    "applicable_to_roles": ["pro", "team_member"]
  }'
```

### Example Response

```json
{
  "options": [
    {
      "id": 1,
      "option_type": "overall_experience",
      "value": "Inadequate",
      "applicable_to_roles": ["pro", "team_member"],
      "min_stars": null,
      "field_tag": null,
      "display_order": 0
    },
    {
      "id": 2,
      "option_type": "overall_experience",
      "value": "Excellent",
      "applicable_to_roles": ["pro", "team_member"],
      "min_stars": null,
      "field_tag": null,
      "display_order": 3
    }
  ]
}
```

## Rating Options Data

The API provides predefined rating options organized by type:

### **Overall Experience Options** (40 options)
Available for all star ratings (1-5 stars), applicable to 'pro' and 'team_member' roles:
- Inadequate, Mediocre, Adequate, Excellent, Outstanding
- Deplorable, Substandard, Average, Impressive, Superb
- Inappropriate, Inconsistent, Acceptable, Skilled, Exceptional
- And 25 more options...

### **Professional Skills Options** (8 options)
Available only for 5★ ratings, applicable to 'pro' and 'team_member' roles:
- Competent, Knowledgeable, Professional, Ethical
- Reliable, Communicative, Efficient, Perfectionist

### **Personal Skills Options** (8 options)
Available only for 5★ ratings, applicable to 'pro_freelancer', 'pro_team_member', and 'team_member' roles:
- Respectful, Caring, Patient, Kind
- Sociable, Funny, Optimistic, Resilient

### **Branch Rating Options** (40 options)
Available for all star ratings (1-5 stars), applicable to 'branch' role:

- **Overall Quality** (5 options): Poor Branch, Below Average Branch, Average Branch, Good Branch, Superb Branch
- **Facilities** (5 options): Poor Facilities, Below Average Facilities, Average Facilities, Good Facilities, Top Facilities
- **Staff Support** (5 options): Poor Staff Support, Below Average Staff Support, Average Staff Support, Good Staff Support, Exceptional Staff Support
- **Recommendation** (5 options): Poorly Recommendable, Not Recommendable, Fairly Recommendable, Recommendable, Highly Recommendable
- **Design** (5 options): Poor Design, Below Average Design, Fair Design, Cool Design, Top‑Notch Design
- **Team Professionalism** (5 options): Very Unprofessional, Unprofessional, Fairly Professional, Professional, Extremely Professional
- **Location** (5 options): Very Inconvenient Location, Inconvenient Location, Acceptable Location, Convenient Location, Prime Location
- **Comfort** (5 options): Poor Comfort, Below Average Comfort, Average Comfort, Good Comfort, Best‑in‑Class Comfort

### **Team Overall Rating Options** (40 options)
Available for all star ratings (1-5 stars), applicable to 'team_overall' role:

- **Teamwork** (5 options): Disjointed Teamwork, Chaotic Teamwork, Functional Teamwork, Strong Teamwork, Exceptional Teamwork
- **Team Culture** (5 options): Toxic Culture, Disengaged Culture, Neutral Culture, Friendly Culture, Inspiring Culture
- **Coordination** (5 options): Confused Coordination, Inconsistent Coordination, Acceptable Coordination, Seamless Coordination, Flawless Coordination
- **Communication** (5 options): Absent Communication, Unclear Communication, Clear Communication, Strong Communication, Outstanding Communication
- **Professionalism** (5 options): Unprofessional Team, Low Professionalism, Fairly Professional Team, Professional Team, Superb Professionalism
- **Atmosphere** (5 options): Unwelcoming Atmosphere, Disengaged Atmosphere, Welcoming Atmosphere, Supportive Atmosphere, Remarkable Atmosphere
- **Respect** (5 options): Disrespectful Team, Disregarding Team, Courteous Team, Respectful Team, Highly Respectful Team
- **Efficiency** (5 options): Inefficient Team, Limited Efficiency, Acceptable Efficiency, Strong Efficiency, World‑Class Efficiency

## Usage Examples

### Get All Options
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Get Overall Experience Options Only
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "option_type": "overall_experience"
  }'
```

### Get 5-Star Options for Professional Skills
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "option_type": "professional_skills",
    "min_stars": 5
  }'
```

### Get Options for Specific Roles
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "applicable_to_roles": ["pro", "team_member"]
  }'
```

### Get Branch Rating Options
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "applicable_to_roles": ["branch"]
  }'
```

### Get Specific Branch Option Type
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "option_type": "branch_overall_quality",
    "applicable_to_roles": ["branch"]
  }'
```

### Get Team Overall Rating Options
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "applicable_to_roles": ["team_overall"]
  }'
```

### Get Team Member Rating Options
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "applicable_to_roles": ["team_member"]
  }'
```

### Get Specific Team Option Type
```bash
curl -X POST "http://localhost:8080/rating/v1/ratings/options" \
  -H "Content-Type: application/json" \
  -d '{
    "option_type": "team_overall_teamwork",
    "applicable_to_roles": ["team_overall"]
  }'
```
