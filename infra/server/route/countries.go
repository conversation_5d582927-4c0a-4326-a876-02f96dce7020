package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/rating/internal/country/transport"
)

type countryFactory interface {
	SearchCountriesHandler() *transport.SearchCountriesHandler
}

type countryRouter struct {
	baseRoute *echo.Group
	factory   countryFactory
}

func NewCountryRouter(e *echo.Group, factory countryFactory) *countryRouter {
	return &countryRouter{
		baseRoute: e,
		factory:   factory,
	}
}

func (r *countryRouter) Route() {
	countryHandler := r.factory.SearchCountriesHandler()

	group := r.baseRoute.Group("/countries")
	group.GET("", countryHandler.Handle())
}
