package middleware

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/rating/pkg/cognito"
	"net/http"
)

type agentAuthProvider interface {
	IsUserValid(ctx context.Context, userID string, userPoolID string) error
}

type AgentAuth struct {
	clientID   string
	userPoolID string
	provider   agentAuthProvider
}

func NewAgentAuth(provider agentAuthProvider, clientID, userPoolID string) *AgentAuth {
	return &AgentAuth{
		clientID:   clientID,
		userPoolID: userPoolID,
		provider:   provider,
	}
}

func (a *AgentAuth) AgentAuthMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			ctx := c.Request().Context()
			userID := c.Get("userID").(string)

			err := a.provider.IsUserValid(ctx, userID, a.userPoolID)
			if err != nil {
				if errors.Is(err, cognito.ErrInvalidUser) {
					return c.JSON(http.StatusForbidden, ErrorResponse{
						Error:   "forbidden",
						Message: err.Error(),
					})
				}

				return c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "internal server error",
					Message: "failed to check if user is an agent",
				})
			}

			return next(c)
		}
	}
}
