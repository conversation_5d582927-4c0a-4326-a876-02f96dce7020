package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"gitlab.viswalslab.com/backend/standard/v2/vlog"

	"github.com/joho/godotenv"
)

// Configuration represents the application settings.
type Configuration struct {
	AppName                       string
	Environment                   string
	LogLevel                      string
	Host                          string
	Port                          string
	IsCreateAccountByQueueEnabled bool

	DataBase DataBase
	PatientService
	Upload
	Bucket
	Messaging
	UserAuth  UserAuth
	AgentAuth AgentAuth
}

type DataBase struct {
	DriverName string
	URL        string
}

type PatientService struct {
	BaseURL      string
	ClientName   string
	ClientId     string
	ClientSecret string
	AuthorizeUrl string
	Scope        string
}

type Upload struct {
	FileMaxSize int64
}

// Bucket represents the bucket configuration.
type Bucket struct {
	Name        string
	Endpoint    string
	Region      string
	AccessKeyID string
	SecretKey   string
	Prefix      string
}

// Messaging represents the messaging configuration.
type Messaging struct {
	BrokerURL   string
	Heartbeat   time.Duration
	RetryConfig RetryConfig

	// queue for consumer
	AccountsQueueName    string
	GemCreationQueueName string
	RatingFormQueueName  string

	// exchange for publisher
	AccountVerificationExchangeName string
	AccountRequestExchangeName      string
	RatingFormExchangeName          string

	// routing keys
	AccountOnboardRoutingKey  string
	AccountCreationRoutingKey string
	AccountRequestRoutingKey  string
	RatingFormRoutingKey      string
}

type RetryConfig struct {
	InitialInterval time.Duration
	Multiplier      float64
	MaxInterval     time.Duration
}

type UserAuth struct {
	AccessKeyID     string
	SecretAccessKey string
	UserPoolID      string
	ClientID        string
	DefaultRegion   string
}

type AgentAuth struct {
	AccessKeyID     string
	SecretAccessKey string
	UserPoolID      string
	ClientID        string
	DefaultRegion   string
}

func FromEnv() Configuration {
	var configuration Configuration

	err := godotenv.Load()
	if err != nil && os.Getenv("ENVIRONMENT") == "local" {
		log.Fatal("error loading .env file")
	}

	configuration.AppName = os.Getenv("APP_NAME")
	configuration.Environment = os.Getenv("ENVIRONMENT")
	configuration.LogLevel = os.Getenv("LOG_LEVEL")
	configuration.Host = os.Getenv("HOST")
	configuration.Port = os.Getenv("PORT")

	configuration.DataBase = DataBase{
		DriverName: "postgres",
		URL:        os.Getenv("DATABASE_URL"),
	}

	fileMaxSize, err := strconv.ParseInt(os.Getenv("UPLOAD_MAX_FILE_SIZE"), 10, 64)
	if err != nil {
		fileMaxSize = 5 * 1024 * 1024 // 5MB
	}
	configuration.Upload.FileMaxSize = fileMaxSize

	configuration.Bucket = Bucket{
		Name:        os.Getenv("BUCKET_NAME"),
		Prefix:      os.Getenv("BUCKET_PREFIX"),
		Endpoint:    os.Getenv("BUCKET_ENDPOINT"),
		Region:      os.Getenv("BUCKET_REGION"),
		AccessKeyID: os.Getenv("AWS_S3_IAM_R4B_ACCESS_KEY"),
		SecretKey:   os.Getenv("AWS_S3_IAM_R4B_SECRET_KEY"),
	}

	configuration.UserAuth = UserAuth{
		AccessKeyID:     os.Getenv("AWS_COGNITO_IAM_R4B_ACCESS_KEY"),
		SecretAccessKey: os.Getenv("AWS_COGNITO_IAM_R4B_SECRET_KEY"),
		UserPoolID:      os.Getenv("AWS_COGNITO_PATIENT_USER_POOL_ID"),
		ClientID:        os.Getenv("AWS_COGNITO_R4B_IGNITE_APP_CLIENT_ID"),
		DefaultRegion:   os.Getenv("AWS_COGNITO_PATIENT_USER_REGION"),
	}

	configuration.AgentAuth = AgentAuth{
		AccessKeyID:     os.Getenv("AWS_COGNITO_IAM_R4B_ACCESS_KEY"),
		SecretAccessKey: os.Getenv("AWS_COGNITO_IAM_R4B_SECRET_KEY"),
		UserPoolID:      os.Getenv("AWS_COGNITO_AGENT_USER_POOL_ID"),
		ClientID:        os.Getenv("AWS_COGNITO_CC_ACCOUNT_R4B_IGNITE_APP_CLIENT_ID"),
		DefaultRegion:   os.Getenv("AWS_COGNITO_AGENT_USER_REGION"),
	}

	QueueHeartbeat := 60 * time.Second
	queueHeartbeat := os.Getenv("RABBITMQ_HEARTBEAT")
	if queueHeartbeat != "" {
		QueueHeartbeat, err = time.ParseDuration(queueHeartbeat)
		if err != nil {
			log.Fatal("error parsing RabbitMQ heartbeat time", vlog.F("error", err))
		}
	}

	InitialInterval := 2 * time.Second
	initialInterval := os.Getenv("RABBITMQ_RETRY_INITIAL_INTERVAL")
	if initialInterval != "" {
		InitialInterval, err = time.ParseDuration(initialInterval)
		if err != nil {
			log.Fatal("error parsing RabbitMQ retry initial interval", vlog.F("error", err))
		}
	}

	Multiplier := 1.0
	multiplier := os.Getenv("RABBITMQ_RETRY_MULTIPLIER")
	if multiplier != "" {
		Multiplier, err = strconv.ParseFloat(multiplier, 64)
		if err != nil {
			log.Fatal("error parsing RabbitMQ retry multiplier", vlog.F("error", err))
		}
	}

	MaxInterval := 3 * time.Minute
	maxInterval := os.Getenv("RABBITMQ_RETRY_MAX_INTERVAL")
	if maxInterval != "" {
		MaxInterval, err = time.ParseDuration(maxInterval)
		if err != nil {
			log.Fatal("error parsing RabbitMQ retry max interval", vlog.F("error", err))
		}
	}

	configuration.Messaging.BrokerURL = os.Getenv("BROKER_URL")
	configuration.Messaging.AccountsQueueName = os.Getenv("MESSAGING_ACCOUNTS_QUEUE_NAME")
	configuration.Messaging.GemCreationQueueName = os.Getenv("MESSAGING_GEM_CREATION_QUEUE_NAME")
	configuration.Messaging.RatingFormQueueName = os.Getenv("MESSAGING_RATING_FORM_QUEUE_NAME")

	configuration.Messaging.AccountVerificationExchangeName = os.Getenv("MESSAGING_ACCOUNT_VERIFICATION_EXCHANGE_NAME")
	configuration.Messaging.AccountRequestExchangeName = os.Getenv("MESSAGING_ACCOUNT_REQUEST_EXCHANGE_NAME")
	configuration.Messaging.RatingFormExchangeName = os.Getenv("MESSAGING_RATING_FORM_EXCHANGE_NAME")

	configuration.Messaging.AccountOnboardRoutingKey = os.Getenv("MESSAGING_ACCOUNTS_ONBOARD_ROUTING_KEY")
	configuration.Messaging.AccountCreationRoutingKey = os.Getenv("MESSAGING_ACCOUNTS_CREATE_ROUTING_KEY")
	configuration.Messaging.AccountRequestRoutingKey = os.Getenv("MESSAGING_ACCOUNTS_REQUEST_ROUTING_KEY")
	configuration.Messaging.RatingFormRoutingKey = os.Getenv("MESSAGING_RATING_FORM_ROUTING_KEY")

	configuration.Messaging.Heartbeat = QueueHeartbeat
	configuration.Messaging.RetryConfig = RetryConfig{
		InitialInterval: InitialInterval,
		Multiplier:      Multiplier,
		MaxInterval:     MaxInterval,
	}

	return configuration
}
