package cognito

import (
	"context"
	"errors"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsCognito "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"

	"gitlab.viswalslab.com/backend/rating/core"
)

var (
	ErrInvalidUser = errors.New("invalid user: ")
)

func (c *Cognito) IsUserValid(ctx context.Context, userID string, userPoolID string) error {
	resp, err := c.Client.AdminGetUser(ctx, &awsCognito.AdminGetUserInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(userID),
	})
	if err != nil {
		return err
	}

	if !resp.Enabled {
		return errors.Join(ErrInvalidUser, errors.New("user is not enabled"))
	}

	if resp.UserStatus != types.UserStatusTypeConfirmed {
		return errors.Join(ErrInvalidUser, errors.New("user is not confirmed"))
	}

	return nil
}

func (c *Cognito) GetTokenFromRefreshToken(ctx context.Context, refreshToken string, clientID, userPoolID string) (*RefreshTokenOutput, error) {
	req := &awsCognito.AdminInitiateAuthInput{
		AuthFlow:       types.AuthFlowTypeRefreshTokenAuth,
		ClientId:       aws.String(clientID),
		UserPoolId:     aws.String(userPoolID),
		AuthParameters: map[string]string{"REFRESH_TOKEN": refreshToken},
	}

	resp, err := c.Client.AdminInitiateAuth(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.AuthenticationResult == nil {
		return nil, errors.New("authentication result is not found in response")
	}

	if resp.AuthenticationResult.AccessToken == nil {
		return nil, errors.New("access token is not found in response")
	}
	if resp.AuthenticationResult.RefreshToken == nil {
		// as refresh token rotation is disabled, we can reuse the same token
		resp.AuthenticationResult.RefreshToken = aws.String(refreshToken)
	}

	return &RefreshTokenOutput{
		AccessToken:  *resp.AuthenticationResult.AccessToken,
		RefreshToken: *resp.AuthenticationResult.RefreshToken,
		ExpiresIn:    time.Now().UTC().Add(time.Duration(resp.AuthenticationResult.ExpiresIn) * time.Second),
		TokenType:    *resp.AuthenticationResult.TokenType,
	}, nil
}

func (c *Cognito) SignIn(ctx context.Context, clientID string, userPoolID string, input *SignInInput) (*SignInOutput, error) {
	req := &awsCognito.AdminInitiateAuthInput{
		AuthFlow:   types.AuthFlowTypeAdminUserPasswordAuth,
		UserPoolId: aws.String(userPoolID),
		ClientId:   aws.String(clientID),
		AuthParameters: map[string]string{
			"USERNAME": input.UserName,
			"PASSWORD": input.Password,
		},
	}

	resp, err := c.Client.AdminInitiateAuth(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.Join(core.ErrNoItemFound, errors.New("response not found from aws"))
	}

	if resp.AuthenticationResult == nil {
		return nil, errors.Join(core.ErrNoItemFound, errors.New("no result found from aws"))
	}

	return &SignInOutput{
		AccessToken:  *resp.AuthenticationResult.AccessToken,
		RefreshToken: *resp.AuthenticationResult.RefreshToken,
		ExpiresIn:    time.Now().UTC().Add(time.Duration(resp.AuthenticationResult.ExpiresIn) * time.Second),
		TokenType:    *resp.AuthenticationResult.TokenType,
	}, nil
}

type SignInInput struct {
	UserName string
	Password string
}

type SignInOutput struct {
	AccessToken  string
	RefreshToken string
	ExpiresIn    time.Time
	TokenType    string
}

type RefreshTokenOutput struct {
	AccessToken  string
	RefreshToken string
	ExpiresIn    time.Time
	TokenType    string
}
