// Code generated by MockGen. DO NOT EDIT.
// Source: submit_rating_form.go
//
// Generated by this command:
//
//	mockgen -source submit_rating_form.go -destination ../../../mocks/internal_mock/rating/usecase/submit_rating_form.go -package usecase
//

// Package usecase is a generated GoMock package.
package usecase

import (
	context "context"
	reflect "reflect"

	repository "gitlab.viswalslab.com/backend/rating/internal/rating/repository"
	gomock "go.uber.org/mock/gomock"
)

// MocksubmitRatingFormRepo is a mock of submitRatingFormRepo interface.
type MocksubmitRatingFormRepo struct {
	ctrl     *gomock.Controller
	recorder *MocksubmitRatingFormRepoMockRecorder
	isgomock struct{}
}

// MocksubmitRatingFormRepoMockRecorder is the mock recorder for MocksubmitRatingFormRepo.
type MocksubmitRatingFormRepoMockRecorder struct {
	mock *MocksubmitRatingFormRepo
}

// NewMocksubmitRatingFormRepo creates a new mock instance.
func NewMocksubmitRatingFormRepo(ctrl *gomock.Controller) *MocksubmitRatingFormRepo {
	mock := &MocksubmitRatingFormRepo{ctrl: ctrl}
	mock.recorder = &MocksubmitRatingFormRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MocksubmitRatingFormRepo) EXPECT() *MocksubmitRatingFormRepoMockRecorder {
	return m.recorder
}

// SubmitRatingForm mocks base method.
func (m *MocksubmitRatingFormRepo) SubmitRatingForm(ctx context.Context, input *repository.SubmitRatingFormInput) (*repository.SubmitRatingFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitRatingForm", ctx, input)
	ret0, _ := ret[0].(*repository.SubmitRatingFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitRatingForm indicates an expected call of SubmitRatingForm.
func (mr *MocksubmitRatingFormRepoMockRecorder) SubmitRatingForm(ctx, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitRatingForm", reflect.TypeOf((*MocksubmitRatingFormRepo)(nil).SubmitRatingForm), ctx, input)
}
