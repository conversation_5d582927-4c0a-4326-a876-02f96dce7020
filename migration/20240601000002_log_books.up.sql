-- Create log_books table
CREATE TABLE log_books (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL,
    entity_role VARCHAR(20) NOT NULL,
    event_type VARCHAR(50),          -- Type of event (e.g., 'rating_form_submitted')
    event_data JSONB,                -- Complete event data as JSON
    processing_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'processed'
    processed_at TIMESTAMPTZ,        -- When processing completed
    correlation_id UUID,             -- Links to RabbitMQ message correlation ID
    created_at TIMESTAMPTZ DEFAULT NOW()
);