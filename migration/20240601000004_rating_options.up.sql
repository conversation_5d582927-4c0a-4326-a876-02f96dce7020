-- Create rating_options table
CREATE TABLE rating_options (
    id SERIAL PRIMARY KEY,
    option_type VARCHAR(50) NOT NULL,  -- e.g., 'overall_experience', 'professional_skills'
    option_value VARCHAR(100) NOT NULL, -- Display text 
    applicable_to_roles VARCHAR(20)[], -- ['user', 'pro', 'team_member', 'branch']
    direction VARCHAR(10)[],           -- ['given', 'received'] or NULL for both
    min_stars INT,                     -- NULL if applies to all ratings
    field_tag VARCHAR(50),             -- NULL for all fields
    display_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for rating_options
CREATE INDEX idx_option_type ON rating_options(option_type);
CREATE INDEX idx_applicable_roles ON rating_options USING GIN(applicable_to_roles);