# Database Migrations

This directory contains database migrations for the rating system in proper sequential order.

## Migration Order

### 1. `20240601000000_uuid_extension`
- **Purpose**: Enable UUID extension for generating UUIDs
- **Creates**: `uuid-ossp` PostgreSQL extension
- **Dependencies**: None (must be first)

### 2. `20240601000001_interactions`
- **Purpose**: Create main interactions table for rating data
- **Creates**: `Interactions` table with core rating fields + processing columns
- **Includes**: `form_data` (JSONB), `processing_status`, `processed_at`
- **Dependencies**: UUID extension

### 3. `20240601000002_log_books`
- **Purpose**: Create log books table for audit trail
- **Creates**: `log_books` table with event logging + processing columns
- **Includes**: `event_type`, `event_data` (JSONB), `processing_status`, `processed_at`, `correlation_id`
- **Dependencies**: UUID extension

### 4. `20240601000003_log_book_interactions`
- **Purpose**: Create linking table between log books and interactions
- **Creates**: `log_book_interactions` table
- **Dependencies**: `log_books`, `Interactions`

### 5. `20240601000004_rating_options`
- **Purpose**: Create rating options table for predefined rating values
- **Creates**: `rating_options` table
- **Dependencies**: None

### 6. `20240601000005_insert_rating_options_data`
- **Purpose**: Insert all predefined rating options data
- **Creates**: 136 rating options across all categories
- **Data Includes**:
  - 40 Overall Experience options (pro_freelancer, pro_team_member, team_member)
  - 8 Professional Skills options (5★ only)
  - 8 Personal Skills options (5★ only)
  - 40 Branch rating options (8 categories × 5 levels)
  - 40 Team overall rating options (8 categories × 5 levels)
- **Dependencies**: `rating_options` table

### 7. `20240601000006_rating_stats`
- **Purpose**: Create rating statistics table and performance indexes
- **Creates**: `entity_rating_stats` table for aggregated rating statistics
- **Creates**: Performance indexes for all tables (log_books, interactions, stats)
- **Dependencies**: `log_books`, `Interactions`
- **Note**: Notifications handled by separate notification microservice

## Running Migrations

```bash
# Apply all migrations
migrate -path ./migration -database "your-database-url" up

# Rollback last migration
migrate -path ./migration -database "your-database-url" down 1

# Check migration status
migrate -path ./migration -database "your-database-url" version
```

## Migration Features

- **Sequential Numbering**: Proper order ensures dependencies are met
- **Atomic Operations**: Each migration is atomic (all or nothing)
- **Rollback Support**: All migrations have corresponding down files
- **Performance Optimized**: Includes proper indexes for query performance
- **Log Books Pattern**: Implements audit trail and event sourcing
- **Comprehensive Data**: Includes all rating options for production use

## Database Schema Overview

After all migrations:
- **Core Tables**: `Interactions`, `log_books`, `log_book_interactions`
- **Configuration**: `rating_options` (136 predefined options)
- **Analytics**: `entity_rating_stats` (aggregated rating data)
- **Extensions**: `uuid-ossp` for UUID generation
- **Indexes**: Optimized for rating queries and analytics

## Microservice Architecture

This rating service focuses solely on:
- ✅ **Rating Data Storage**: Core rating interactions
- ✅ **Rating Analytics**: Aggregated statistics
- ✅ **Queue Processing**: Event-driven rating processing
- ✅ **Audit Trail**: Complete log books pattern

**Not included** (handled by other microservices):
- ❌ **Notifications**: Separate notification service
- ❌ **User Management**: Separate user service
- ❌ **Payment Processing**: Separate payment service
