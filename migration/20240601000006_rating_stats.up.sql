-- Create entity rating statistics table for aggregated rating data
CREATE TABLE entity_rating_stats (
    id SERIAL PRIMARY KEY,
    entity_id UUID NOT NULL,
    entity_role VARCHAR(20) NOT NULL,
    total_ratings INTEGER NOT NULL DEFAULT 0,
    total_stars INTEGER NOT NULL DEFAULT 0,
    average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,
    last_rating_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(entity_id, entity_role)
);

-- Create indexes for better performance
CREATE INDEX idx_log_books_event_type ON log_books(event_type);
CREATE INDEX idx_log_books_processing_status ON log_books(processing_status);
CREATE INDEX idx_log_books_processed_at ON log_books(processed_at);
CREATE INDEX idx_log_books_correlation_id ON log_books(correlation_id);
CREATE INDEX idx_log_books_entity_id_role ON log_books(entity_id, entity_role);


CREATE INDEX idx_entity_rating_stats_entity_id ON entity_rating_stats(entity_id);
CREATE INDEX idx_entity_rating_stats_entity_role ON entity_rating_stats(entity_role);
CREATE INDEX idx_entity_rating_stats_average_rating ON entity_rating_stats(average_rating);

CREATE INDEX idx_interactions_processing_status ON Interactions(processing_status);
CREATE INDEX idx_interactions_processed_at ON Interactions(processed_at);
