-- Create log_book_interactions table
CREATE TABLE log_book_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_book_id UUID NOT NULL,
    interaction_id UUID NOT NULL,
    interaction_type VARCHAR(30) NOT NULL,
    direction VARCHAR(10) NOT NULL, -- 'given' or 'received'
    FOREIGN KEY (log_book_id) REFERENCES log_books(id)
);

-- Create index on log_book_interactions
CREATE INDEX idx_lbi_log_book ON log_book_interactions(log_book_id);