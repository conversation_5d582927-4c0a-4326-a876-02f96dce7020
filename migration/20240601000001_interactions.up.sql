-- Create Interactions table
CREATE TABLE Interactions (
    interaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL,
    from_role VARCHAR(20) NOT NULL, -- 'user', 'pro', 'team_member'
    from_entity_id UUID NOT NULL,   -- ID of the rater
    to_role VARCHAR(20) NOT NULL,   -- 'user', 'pro', 'branch', 'team_member'
    to_entity_id UUID NOT NULL,     -- ID of the rated entity
    field_tag VARCHAR(50) NOT NULL, -- Skin/field context
    star_rating SMALLINT NOT NULL CHECK (star_rating BETWEEN 1 AND 5),
    overall_experience VARCHAR(50) NOT NULL, -- Selected enum option
    professional_skills VARCHAR(50), -- Nullable (only for 5★ Pro ratings)
    personal_skills VARCHAR(50),     -- Nullable (only for 5★ Pro ratings)
    thanking_note TEXT,              -- Nullable (only for 5★ ratings)
    form_data JSONB,                 -- Additional form data as JSO<PERSON>
    processing_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'processed'
    processed_at TIMESTAMPTZ,       -- When queue processing completed
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_invalidated BOOLEAN NOT NULL DEFAULT FALSE -- Marked true if refunded
);